'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { BarChart3, MessageCircle, LogOut } from 'lucide-react';
import Image from 'next/image';
import ChatAssistant from '@/components/ChatAssistant';
import DataAnalysis from '@/components/DataAnalysis';

interface Message {
  role: 'user' | 'assistant';
  content: string;
}

export default function Home() {
  const [activeTab, setActiveTab] = useState<'chat' | 'analysis'>('chat');
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 md:h-20">
            <div className="flex items-center space-x-2 md:space-x-4 min-w-0 flex-1">
              <div className="p-1.5 md:p-2 bg-white rounded-lg md:rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex-shrink-0">
                <Image
                  src="/demokrasilogo.png"
                  alt="Demokrasi Üniversitesi Logo"
                  width={32}
                  height={32}
                  className="object-contain md:w-12 md:h-12"
                />
              </div>
              <div className="min-w-0 flex-1">
                <h1 className="text-lg md:text-2xl font-bold text-slate-800 truncate">
                  Kırılma Tokluğu Asistanı
                </h1>
                <p className="text-xs md:text-sm text-slate-600 truncate">
                  Yapay Zeka Destekli Hesaplama ve Analiz
                </p>
              </div>
            </div>

            {/* Navigation Tabs and Logout in Header */}
            <div className="flex items-center space-x-1 md:space-x-3 flex-shrink-0">
              <button
                onClick={() => setActiveTab('chat')}
                className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ${
                  activeTab === 'chat'
                    ? 'bg-blue-600 text-white shadow-md hover:scale-105'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-gray-50'
                }`}
              >
                <MessageCircle className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">Kırılma Tokluğu Asistanı</span>
                <span className="sm:hidden">Chat</span>
              </button>
              <button
                onClick={() => setActiveTab('analysis')}
                className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ${
                  activeTab === 'analysis'
                    ? 'bg-blue-600 text-white shadow-md hover:scale-105'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-gray-50'
                }`}
              >
                <BarChart3 className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">Laboratuvar Veri Analizi</span>
                <span className="sm:hidden">Analiz</span>
              </button>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200 hover:scale-105"
                title="Çıkış Yap"
              >
                <LogOut className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden lg:inline">Çıkış</span>
              </button>
            </div>
          </div>
        </div>
      </header>



      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'chat' && (
          <ChatAssistant
            messages={chatMessages}
            setMessages={setChatMessages}
          />
        )}
        {activeTab === 'analysis' && <DataAnalysis />}
      </main>
    </div>
  );
}
