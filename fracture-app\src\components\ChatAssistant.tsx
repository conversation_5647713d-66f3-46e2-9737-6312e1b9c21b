'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, Co<PERSON>, Check } from 'lucide-react';

interface Message {
  role: 'user' | 'assistant';
  content: string;
}

interface ChatAssistantProps {
  messages: Message[];
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
}

export default function ChatAssistant({ messages, setMessages }: ChatAssistantProps) {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleQuickAction = (action: 'calculation' | 'theory' | 'variables') => {
    let message = '';

    switch (action) {
      case 'calculation':
        message = 'Kırılma tokluğu hesaplaması yapmak istiyorum. Hangi değerleri girmeliyim?';
        break;
      case 'theory':
        message = 'Kırılma tokluğu nedir? Teorik bilgi verebilir misin?';
        break;
      case 'variables':
        message = 'Pmax, B, D ve Y* değişkenlerinin ne anlama geldiğini açıklar mısın?';
        break;
    }

    setInput(message);
    // Otomatik olarak mesajı gönder
    setTimeout(() => {
      sendMessage();
    }, 100);
  };

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = { role: 'user', content: input };
    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages: newMessages }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let assistantMessage = '';
      setMessages([...newMessages, { role: 'assistant', content: '' }]);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') break;
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.text) {
                assistantMessage += parsed.text;
                setMessages([...newMessages, { role: 'assistant', content: assistantMessage }]);
              }
            } catch {
              // Ignore parsing errors
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages([...newMessages, { 
        role: 'assistant', 
        content: 'Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.' 
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 h-[calc(100vh-200px)] min-h-[500px] max-h-[800px] flex flex-col">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 md:p-6 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-slate-700 mt-8">
            <div className="p-4 bg-blue-600 rounded-2xl w-16 h-16 mx-auto mb-6 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
              <Bot className="h-8 w-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-slate-800">
              Kırılma Tokluğu Asistanına Hoş Geldiniz!
            </p>
            <p className="text-base mt-3 mb-8 font-medium text-slate-600">
              Hesaplama yapmak için değerleri girin veya teorik sorularınızı sorun.
            </p>

            {/* Quick Action Buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 max-w-2xl mx-auto px-2 md:px-0">
              <button
                onClick={() => handleQuickAction('calculation')}
                className="flex flex-col items-center p-3 md:p-4 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 hover:border-green-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"
              >
                <div className="p-2 bg-green-600 rounded-lg mb-2">
                  <span className="text-white text-lg">✅</span>
                </div>
                <span className="font-semibold text-slate-800 text-sm md:text-base">Hesaplama Yap</span>
                <span className="text-xs md:text-sm text-slate-600 mt-1">KIC değeri hesapla</span>
              </button>

              <button
                onClick={() => handleQuickAction('theory')}
                className="flex flex-col items-center p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"
              >
                <div className="p-2 bg-blue-600 rounded-lg mb-2">
                  <span className="text-white text-lg">📘</span>
                </div>
                <span className="font-semibold text-slate-800 text-sm md:text-base">Teorik Bilgi</span>
                <span className="text-xs md:text-sm text-slate-600 mt-1">Kırılma tokluğu nedir?</span>
              </button>

              <button
                onClick={() => handleQuickAction('variables')}
                className="flex flex-col items-center p-3 md:p-4 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"
              >
                <div className="p-2 bg-purple-600 rounded-lg mb-2">
                  <span className="text-white text-lg">ℹ️</span>
                </div>
                <span className="font-semibold text-slate-800 text-sm md:text-base">Değişken Açıklamaları</span>
                <span className="text-xs md:text-sm text-slate-600 mt-1">Pmax, B, D, Y* nedir?</span>
              </button>
            </div>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} group`}
          >
            <div
              className={`max-w-[85%] md:max-w-[80%] rounded-xl px-3 md:px-4 py-2 md:py-3 relative shadow-sm ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-slate-800'
              }`}
            >
              <div className="flex items-start space-x-2">
                {message.role === 'assistant' && (
                  <Bot className="h-4 w-4 mt-0.5 text-blue-600" />
                )}
                {message.role === 'user' && (
                  <User className="h-4 w-4 mt-0.5 text-white" />
                )}
                <div className="whitespace-pre-wrap flex-1">{message.content}</div>
              </div>

              {/* Copy button */}
              <button
                onClick={() => copyToClipboard(message.content, index)}
                className={`absolute top-2 right-2 p-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-105 ${
                  message.role === 'user'
                    ? 'hover:bg-blue-700 text-white'
                    : 'hover:bg-gray-200 text-slate-600'
                }`}
                title="Metni kopyala"
              >
                {copiedIndex === index ? (
                  <Check className="h-3 w-3" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </button>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-xl px-4 py-3 shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="p-1 bg-blue-600 rounded-lg">
                  <Bot className="h-3 w-3 text-white" />
                </div>
                <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-slate-600">Yazıyor...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 md:p-6 border-t border-gray-100">
        <div className="flex space-x-2 md:space-x-3">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Değerleri girin veya bir soru sorun..."
            className="flex-1 resize-none border border-gray-200 rounded-xl px-3 md:px-4 py-2 md:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500 text-sm md:text-base"
            rows={2}
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={!input.trim() || isLoading}
            className="bg-blue-600 text-white rounded-xl px-4 md:px-6 py-2 md:py-3 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
