import streamlit as st
import anthropic
import os
import math
import pandas as pd # Data processing added
import matplotlib.pyplot as plt # Plotting added

# Prioritize environment variable for Render, Vercel, and local development.
# This is the most reliable method.
api_key = os.environ.get("ANTHROPIC_API_KEY")

# If environment variable is not found (e.g., running on Streamlit Cloud), try st.secrets.
if not api_key:
    try:
        api_key = st.secrets.get("ANTHROPIC_API_KEY")
    except (AttributeError, FileNotFoundError):
        api_key = None # Keep as None in case of error

# Final check if API key is found
if not api_key:
    st.error(
        "🚨 Anthropic API key not found! "
        "If deploying to Render, make sure you have added an environment variable "
        "named `ANTHROPIC_API_KEY` under the 'Environment' tab."
    )
    st.stop()

# --- Initialize Anthropic Client ---
client = anthropic.Anthropic(api_key=api_key)

# --- Model and System Prompt ---
# One of the latest and most capable models we will use
MODEL_NAME = "claude-3-7-sonnet-20250219"

# System prompt defining AI's role and "knowledge"
SYSTEM_PROMPT = """
You are an AI assistant specialized in materials science and mechanics.
Your main task is to help the user with the "Fracture Toughness (K_IC)" formula.

Here are the basic formula and variables you need to know:

FORMULA:
K_IC = (P_max / (B * sqrt(D))) * Y_star_min

VARIABLES:
- K_IC: Fracture Toughness (Unit: MPa)
- P_max: Maximum Fracture Load (Unit: MN - Meganewton)
- B: Sample Thickness (Unit: m - Meter)
- D: Sample Diameter (Unit: m - Meter)
- Y_star_min: Critical Dimensionless Shape Factor (Unit: Dimensionless)

INTERACTION RULES:
1. When the user wants to calculate, ask for the necessary P_max, B, D, and Y_star_min values.
2. IMPORTANT: Always check if the user has provided units for each value. If units are missing, ask the user to specify the correct units:
   - P_max should be in MN (Meganewton) or specify the unit
   - B should be in m (Meter) or specify the unit
   - D should be in m (Meter) or specify the unit
   - Y_star_min is dimensionless (no unit needed)
3. If the user provides all values WITH proper units, calculate the K_IC value step-by-step using the formula.
4. Clearly show the calculation steps (formula, substitution with units, and result).
5. Briefly explain what the result means.
6. Answer theoretical questions about the formula or variables (e.g., "What is fracture toughness?") based on your expert knowledge.
7. Always use a polite, professional, and helpful language. Always speak Turkish Language.
"""

# --- Streamlit Interface ---
st.set_page_config(page_title="Kırılma Tokluğu Asistanı", page_icon="⚙️", layout="wide")

st.title("⚙️ Kırılma Tokluğu Hesaplama, Laboratuvar Veri Analizi ve Görselleştirme")
st.caption(f"Yapay Zeka Destekli Kırılma Tokluğu Hesaplama Uygulaması - TEST SÜRÜMÜ")

# Create tabs
tab1, tab2 = st.tabs(["Kırılma Tokluğu Asistanı", "Laboratuvar Veri Analizi ve Görselleştirme Uygulaması"])

with tab1:
    st.header("Sohbet Asistanı")
    st.write("Kırılma tokluğu hesaplamaları veya teorik sorularınız için burayı kullanın.")

    # --- Manage Chat History ---
    # We store chat history using Streamlit's session_state feature.
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Display past messages on the screen
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # --- Get User Input and Generate Response ---
    if prompt := st.chat_input("Değerleri girin veya bir soru sorun...", key="chat_input"):
        # Add user's message to history and display on screen
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Get response from AI
        with st.chat_message("assistant"):
            try:
                # Send request to Claude API (stream=True for real-time response flow)
                stream = client.messages.create(
                    model=MODEL_NAME,
                    system=SYSTEM_PROMPT,
                    messages=st.session_state.messages,
                    max_tokens=2048,
                    stream=True,
                )

                # Accumulate all text parts from the stream
                full_response_content = ""
                # Start real-time writing with a temporary placeholder
                response_placeholder = st.empty() 

                for chunk in stream:
                    # Check text_delta of content_block_delta type chunks
                    if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                        full_response_content += chunk.delta.text
                        response_placeholder.markdown(full_response_content + "▌") # Add blinking cursor for streaming effect
                
                response_placeholder.markdown(full_response_content) # Display final state without cursor

                # Add the completed response to chat history
                st.session_state.messages.append({"role": "assistant", "content": full_response_content})

            except Exception as e:
                st.error(f"An error occurred: {e}")

with tab2:
    st.header("Laboratuvar Veri Analizi ve Görselleştirme")
    st.write("Test sonuçlarınızı girin ve grafiklerini oluşturun.")

    # Tablo 1 Data Entry
    st.subheader("Tablo 1: SCB Kırılma Tokluğu Test Sonuçları")
    st.write("Aşağıdaki tabloyu düzenleyebilir veya yeni veriler ekleyebilirsiniz.")

    # Define initial data for Table 1 as a DataFrame
    initial_table1_data = {
        'SCB Numuneleri': [
            'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°',
            'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°'
        ],
        'Pmax (N)': [
            370, 190, 195, 250, 510, 320, 296, 452, 384, 348,
            340, 720, 630, 486, 398, 615, 532, 478, 548
        ],
        'KIC (MPa√m)': [
            0.16, 0.10, 0.1, 0.11, 0.22, 0.14, 0.13, 0.19, 0.16, 0.14,
            0.15, 0.31, 0.27, 0.21, 0.17, 0.26, 0.23, 0.21, 0.23
        ]
    }
    df_table1 = pd.DataFrame(initial_table1_data)

    # Display data as an editable table with st.data_editor
    # Added the 'ORTALAMA' rows back to the data_editor for display purposes only,
    # but actual average calculation will be dynamic.
    initial_table1_data_display = {
        'SCB Numuneleri': [
            'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'β=0°', 'ORTALAMA',
            'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'β=30°', 'ORTALAMA'
        ],
        'Pmax (N)': [
            370, 190, 195, 250, 510, 320, 296, 452, 384, 348, 331.5, # Avg for β=0°
            340, 720, 630, 486, 398, 615, 532, 478, 548, 527.4      # Avg for β=30°
        ],
        'KIC (MPa√m)': [
            0.16, 0.10, 0.1, 0.11, 0.22, 0.14, 0.13, 0.19, 0.16, 0.14, 0.15, # Avg for β=0°
            0.15, 0.31, 0.27, 0.21, 0.17, 0.26, 0.23, 0.21, 0.23, 0.23      # Avg for β=30°
        ]
    }
    df_table1_display = pd.DataFrame(initial_table1_data_display)

    edited_df_table1 = st.data_editor(df_table1_display, num_rows="dynamic", use_container_width=True, key="table1_editor")


    if st.button("Tablo 1 KIC Grafiğini Oluştur", key="plot_kic_button"):
        st.subheader("KIC Değerleri Grafiği (Pmax vs KIC)")
        
        # Filter out "ORTALAMA" rows for plotting
        df_plot_data = edited_df_table1[edited_df_table1['SCB Numuneleri'] != 'ORTALAMA'].copy()

        # Convert to numeric, coercing errors
        df_plot_data['Pmax (N)'] = pd.to_numeric(df_plot_data['Pmax (N)'], errors='coerce')
        df_plot_data['KIC (MPa√m)'] = pd.to_numeric(df_plot_data['KIC (MPa√m)'], errors='coerce')

        # Drop rows with NaN values that resulted from coercion (non-numeric entries)
        df_plot_data.dropna(subset=['Pmax (N)', 'KIC (MPa√m)'], inplace=True)

        fig_kic, ax_kic = plt.subplots(figsize=(10, 6))

        # Plot Beta=0° data and calculate its average dynamically
        df_beta0 = df_plot_data[df_plot_data['SCB Numuneleri'] == 'β=0°']
        if not df_beta0.empty:
            ax_kic.scatter(df_beta0['Pmax (N)'], df_beta0['KIC (MPa√m)'], label='β=0° (Çekme)', color='blue', s=100, alpha=0.7)
            avg_kic_beta0 = df_beta0['KIC (MPa√m)'].mean()
            ax_kic.axhline(y=avg_kic_beta0, color='blue', linestyle='--', label=f'β=0° Ortalama KIC ({avg_kic_beta0:.2f})')
            ax_kic.text(df_plot_data['Pmax (N)'].max(), avg_kic_beta0, f'Avg: {avg_kic_beta0:.2f}', color='blue', va='center', ha='right') # Adjusted ha for better placement


        # Plot Beta=30° data and calculate its average dynamically
        df_beta30 = df_plot_data[df_plot_data['SCB Numuneleri'] == 'β=30°']
        if not df_beta30.empty:
            ax_kic.scatter(df_beta30['Pmax (N)'], df_beta30['KIC (MPa√m)'], label='β=30° (Çekme-Makaslama)', color='red', marker='x', s=100, alpha=0.7)
            avg_kic_beta30 = df_beta30['KIC (MPa√m)'].mean()
            ax_kic.axhline(y=avg_kic_beta30, color='red', linestyle='--', label=f'β=30° Ortalama KIC ({avg_kic_beta30:.2f})')
            ax_kic.text(df_plot_data['Pmax (N)'].max(), avg_kic_beta30, f'Avg: {avg_kic_beta30:.2f}', color='red', va='center', ha='right') # Adjusted ha for better placement


        ax_kic.set_xlabel("Pmax (N)")
        ax_kic.set_ylabel("KIC (MPa√m)")
        ax_kic.set_title("SCB Kırılma Tokluğu Test Sonuçları")
        ax_kic.grid(True)
        ax_kic.legend()
        st.pyplot(fig_kic)
    else:
        st.info("Tablo 1'deki verileri düzenleyebilir ve 'Tablo 1 KIC Grafiğini Oluştur' düğmesine basarak görselleştirebilirsiniz.")


    # Input and Visualization for Load-COD Curve
    st.subheader("Yük-COD Eğrisi Görselleştirme")
    st.write("Yük (kN) ve COD (mm) değerlerini virgülle ayırarak girin (örneğin, 0.01,0.05\n0.02,0.1) ya da sekme/boşlukla ayırabilirsiniz. Alternatif olarak bir CSV dosyası da yükleyebilirsiniz.")

    col1, col2 = st.columns(2)

    with col1:
        uploaded_file = st.file_uploader("CSV Dosyası Yükle (Yük,COD)", type=["csv"], key="cod_csv_uploader")
        if uploaded_file is not None:
            try:
                df_cod = pd.read_csv(uploaded_file)
                st.session_state.cod_data = df_cod.to_csv(index=False) # Save DataFrame back to session state
                st.success("CSV file uploaded successfully!")
            except Exception as e:
                st.error(f"An error occurred while uploading CSV: {e}")
                st.session_state.cod_data = "" # Clear in case of error
        
        # If no data in session state, set initial empty text area
        if "cod_data" not in st.session_state or not st.session_state.cod_data:
            st.session_state.cod_data = (
        "0.00,0.00\n"
        "0.05,0.06\n"
        "0.10,0.12\n"
        "0.15,0.16\n"
        "0.20,0.18\n"
        "0.25,0.195\n"
        "0.30,0.20\n"
        "0.35,0.195\n"
        "0.40,0.18\n"
        "0.45,0.16\n"
        "0.50,0.13\n"
        "0.55,0.10\n"
        "0.60,0.07\n"
        "0.65,0.05\n"
        "0.70,0.03\n"
        "0.75,0.01\n"
        "0.80,0.00" ) # Example data


    with col2:
        cod_input_text = st.text_area("Manuel Yük ve COD Verileri (Yük,COD her satırda)", value=st.session_state.cod_data, height=200, key="cod_text_area")


    if st.button("Yük-COD Eğrisini Oluştur", key="plot_cod_button"):
        if cod_input_text:
            try:
                # Separate incoming data line by line
                lines = cod_input_text.strip().split('\n')
                data = []
                for line in lines:
                    parts = line.split(',')
                    if len(parts) == 2:
                        data.append([float(parts[0].strip()), float(parts[1].strip())])
                
                df_cod_plot = pd.DataFrame(data, columns=['Yük (kN)', 'COD (mm)'])
                
                fig_cod, ax_cod = plt.subplots(figsize=(10, 6))
                ax_cod.plot(df_cod_plot['COD (mm)'], df_cod_plot['Yük (kN)'], marker='o', linestyle='-', color='green', markersize=5)
                ax_cod.set_xlabel("Çatlak Ağız Açıklığı Yer Değiştirme (COD - mm)")
                ax_cod.set_ylabel("Yük (kN)")
                ax_cod.set_title("Tipik Yük-COD Eğrisi")
                ax_cod.grid(True)
                st.pyplot(fig_cod)
                
                st.session_state.cod_data = cod_input_text # Save updated data if successful

            except ValueError:
                st.error("Yük-COD data is in incorrect format. Please enter numeric values (e.g., 0.1,0.05).")
            except Exception as e:
                st.error(f"An error occurred while creating Load-COD graph: {e}")
        else:
            st.warning("Please enter data for Load-COD curve.")
