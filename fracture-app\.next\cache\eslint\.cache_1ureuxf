[{"C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\chat\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\ChatAssistant.tsx": "4", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\DataAnalysis.tsx": "5", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\DataTable.tsx": "6", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\LineChart.tsx": "7", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\ScatterChart.tsx": "8", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\auth\\login\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\auth\\logout\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\middleware.ts": "12"}, {"size": 3145, "mtime": 1749429790135, "results": "13", "hashOfConfig": "14"}, {"size": 590, "mtime": 1749430259188, "results": "15", "hashOfConfig": "14"}, {"size": 4595, "mtime": 1749434036644, "results": "16", "hashOfConfig": "14"}, {"size": 10419, "mtime": 1749433674065, "results": "17", "hashOfConfig": "14"}, {"size": 21341, "mtime": 1749435252729, "results": "18", "hashOfConfig": "14"}, {"size": 6494, "mtime": 1749429026088, "results": "19", "hashOfConfig": "14"}, {"size": 4137, "mtime": 1749435269443, "results": "20", "hashOfConfig": "14"}, {"size": 6291, "mtime": 1749435108126, "results": "21", "hashOfConfig": "14"}, {"size": 1316, "mtime": 1749435148262, "results": "22", "hashOfConfig": "14"}, {"size": 534, "mtime": 1749435172165, "results": "23", "hashOfConfig": "14"}, {"size": 5471, "mtime": 1749435186720, "results": "24", "hashOfConfig": "14"}, {"size": 1326, "mtime": 1749433946223, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "24yeeb", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\ChatAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\DataAnalysis.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\DataTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\LineChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\components\\ScatterChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\auth\\logout\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\middleware.ts", [], []]