<!DOCTYPE html><html lang="tr"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/31864486f7ca46f5.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js"/><script src="/_next/static/chunks/4bd1b696-1d52210eab209fad.js" async=""></script><script src="/_next/static/chunks/684-50581747ceaf7170.js" async=""></script><script src="/_next/static/chunks/main-app-18b79c4c8a6a19ee.js" async=""></script><script src="/_next/static/chunks/ca377847-de843351d3c90f3f.js" async=""></script><script src="/_next/static/chunks/750-8e917e516141ff92.js" async=""></script><script src="/_next/static/chunks/129-53cb11d93feb5f4a.js" async=""></script><script src="/_next/static/chunks/app/page-f4803347226d58b7.js" async=""></script><title>Kırılma Tokluğu Asistanı</title><meta name="description" content="Yapay Zeka Destekli Kırılma Tokluğu Hesaplama ve Laboratuvar Veri Analizi Uygulaması"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c antialiased bg-gray-50"><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-100"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16 md:h-20"><div class="flex items-center space-x-2 md:space-x-4 min-w-0 flex-1"><div class="p-1.5 md:p-2 bg-white rounded-lg md:rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex-shrink-0"><img alt="Demokrasi Üniversitesi Logo" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="object-contain md:w-12 md:h-12" style="color:transparent" srcSet="/_next/image?url=%2Fdemokrasilogo.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2Fdemokrasilogo.png&amp;w=64&amp;q=75 2x" src="/_next/image?url=%2Fdemokrasilogo.png&amp;w=64&amp;q=75"/></div><div class="min-w-0 flex-1"><h1 class="text-lg md:text-2xl font-bold text-slate-800 truncate">Kırılma Tokluğu Asistanı</h1><p class="text-xs md:text-sm text-slate-600 truncate">Yapay Zeka Destekli Hesaplama ve Analiz</p></div></div><div class="flex items-center space-x-1 md:space-x-3 flex-shrink-0"><button class="flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 bg-blue-600 text-white shadow-md hover:scale-105"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle h-3 w-3 md:h-4 md:w-4" aria-hidden="true"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class="hidden sm:inline">Kırılma Tokluğu Asistanı</span><span class="sm:hidden">Chat</span></button><button class="flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 text-slate-700 hover:text-slate-900 hover:bg-gray-50"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column h-3 w-3 md:h-4 md:w-4" aria-hidden="true"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg><span class="hidden sm:inline">Laboratuvar Veri Analizi</span><span class="sm:hidden">Analiz</span></button><button class="flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200 hover:scale-105" title="Çıkış Yap"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-out h-3 w-3 md:h-4 md:w-4" aria-hidden="true"><path d="m16 17 5-5-5-5"></path><path d="M21 12H9"></path><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path></svg><span class="hidden lg:inline">Çıkış</span></button></div></div></div></header><main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="bg-white rounded-2xl shadow-lg border border-gray-100 h-[calc(100vh-200px)] min-h-[500px] max-h-[800px] flex flex-col"><div class="flex-1 overflow-y-auto p-4 md:p-6 space-y-4"><div class="text-center text-slate-700 mt-8"><div class="p-4 bg-blue-600 rounded-2xl w-16 h-16 mx-auto mb-6 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot h-8 w-8 text-white" aria-hidden="true"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg></div><p class="text-2xl font-bold text-slate-800">Kırılma Tokluğu Asistanına Hoş Geldiniz!</p><p class="text-base mt-3 mb-8 font-medium text-slate-600">Hesaplama yapmak için değerleri girin veya teorik sorularınızı sorun.</p><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 max-w-2xl mx-auto px-2 md:px-0"><button class="flex flex-col items-center p-3 md:p-4 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 hover:border-green-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"><div class="p-2 bg-green-600 rounded-lg mb-2"><span class="text-white text-lg">✅</span></div><span class="font-semibold text-slate-800 text-sm md:text-base">Hesaplama Yap</span><span class="text-xs md:text-sm text-slate-600 mt-1">KIC değeri hesapla</span></button><button class="flex flex-col items-center p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"><div class="p-2 bg-blue-600 rounded-lg mb-2"><span class="text-white text-lg">📘</span></div><span class="font-semibold text-slate-800 text-sm md:text-base">Teorik Bilgi</span><span class="text-xs md:text-sm text-slate-600 mt-1">Kırılma tokluğu nedir?</span></button><button class="flex flex-col items-center p-3 md:p-4 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md"><div class="p-2 bg-purple-600 rounded-lg mb-2"><span class="text-white text-lg">ℹ️</span></div><span class="font-semibold text-slate-800 text-sm md:text-base">Değişken Açıklamaları</span><span class="text-xs md:text-sm text-slate-600 mt-1">Pmax, B, D, Y* nedir?</span></button></div></div><div></div></div><div class="p-4 md:p-6 border-t border-gray-100"><div class="flex space-x-2 md:space-x-3"><textarea placeholder="Değerleri girin veya bir soru sorun..." class="flex-1 resize-none border border-gray-200 rounded-xl px-3 md:px-4 py-2 md:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500 text-sm md:text-base" rows="2"></textarea><button disabled="" class="bg-blue-600 text-white rounded-xl px-4 md:px-6 py-2 md:py-3 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send h-4 w-4" aria-hidden="true"><path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path><path d="m21.854 2.147-10.94 10.939"></path></svg></button></div></div></div></main></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[2921,[\"647\",\"static/chunks/ca377847-de843351d3c90f3f.js\",\"750\",\"static/chunks/750-8e917e516141ff92.js\",\"129\",\"static/chunks/129-53cb11d93feb5f4a.js\",\"974\",\"static/chunks/app/page-f4803347226d58b7.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/31864486f7ca46f5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"29FOOHbPpTZvI1zunjA9I\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/31864486f7ca46f5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"tr\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c antialiased bg-gray-50\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"jtFxXP4QsRfOAQ6bAvgtG\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Kırılma Tokluğu Asistanı\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Yapay Zeka Destekli Kırılma Tokluğu Hesaplama ve Laboratuvar Veri Analizi Uygulaması\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>