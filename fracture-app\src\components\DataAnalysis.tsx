'use client';

import { useState } from 'react';
import { BarChart3, Download, Plus, Trash2, Upload, Info, X } from 'lucide-react';
import ScatterChart from './ScatterChart';
import LineChart from './LineChart';
import DataTable from './DataTable';

interface TableData {
  'SCB Numuneleri': string;
  'Pmax (N)': number;
  'KIC (MPa√m)': number;
}

interface CODData {
  'Yük (kN)': number;
  'COD (mm)': number;
}

// Available beta angles (for future use)
// const BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];

const initialTableData: TableData[] = [
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 370, 'KIC (MPa√m)': 0.16 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 190, 'KIC (MPa√m)': 0.10 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 195, 'KIC (MPa√m)': 0.10 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 250, 'KIC (MPa√m)': 0.11 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 510, 'KIC (MPa√m)': 0.22 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 320, 'KIC (MPa√m)': 0.14 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 296, 'KIC (MPa√m)': 0.13 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 452, 'KIC (MPa√m)': 0.19 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 384, 'KIC (MPa√m)': 0.16 },
  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 348, 'KIC (MPa√m)': 0.14 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 340, 'KIC (MPa√m)': 0.15 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 720, 'KIC (MPa√m)': 0.31 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 630, 'KIC (MPa√m)': 0.27 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 486, 'KIC (MPa√m)': 0.21 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 398, 'KIC (MPa√m)': 0.17 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 615, 'KIC (MPa√m)': 0.26 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 532, 'KIC (MPa√m)': 0.23 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 478, 'KIC (MPa√m)': 0.21 },
  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 548, 'KIC (MPa√m)': 0.23 },
];

const initialCODData: CODData[] = [
  { 'Yük (kN)': 0.00, 'COD (mm)': 0.00 },
  { 'Yük (kN)': 0.05, 'COD (mm)': 0.06 },
  { 'Yük (kN)': 0.10, 'COD (mm)': 0.12 },
  { 'Yük (kN)': 0.15, 'COD (mm)': 0.16 },
  { 'Yük (kN)': 0.20, 'COD (mm)': 0.18 },
  { 'Yük (kN)': 0.25, 'COD (mm)': 0.195 },
  { 'Yük (kN)': 0.30, 'COD (mm)': 0.20 },
  { 'Yük (kN)': 0.35, 'COD (mm)': 0.195 },
  { 'Yük (kN)': 0.40, 'COD (mm)': 0.18 },
  { 'Yük (kN)': 0.45, 'COD (mm)': 0.16 },
  { 'Yük (kN)': 0.50, 'COD (mm)': 0.13 },
  { 'Yük (kN)': 0.55, 'COD (mm)': 0.10 },
  { 'Yük (kN)': 0.60, 'COD (mm)': 0.07 },
  { 'Yük (kN)': 0.65, 'COD (mm)': 0.05 },
  { 'Yük (kN)': 0.70, 'COD (mm)': 0.03 },
  { 'Yük (kN)': 0.75, 'COD (mm)': 0.01 },
  { 'Yük (kN)': 0.80, 'COD (mm)': 0.00 },
];

export default function DataAnalysis() {
  const [tableData, setTableData] = useState<TableData[]>(initialTableData);
  const [codData, setCodData] = useState<CODData[]>(initialCODData);
  const [showKICChart, setShowKICChart] = useState(false);
  const [showCODChart, setShowCODChart] = useState(false);
  const [showSCBUploadModal, setShowSCBUploadModal] = useState(false);
  const [showCODUploadModal, setShowCODUploadModal] = useState(false);

  const addTableRow = () => {
    setTableData([...tableData, { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 0, 'KIC (MPa√m)': 0 }]);
  };

  // Calculate averages for each beta angle dynamically when needed
  // const calculateAverages = () => {
  //   const averages: Record<string, { avgPmax: number; avgKIC: number; count: number }> = {};
  //   BETA_ANGLES.forEach(angle => {
  //     const angleData = tableData.filter(row => row['SCB Numuneleri'] === angle);
  //     if (angleData.length > 0) {
  //       const avgPmax = angleData.reduce((sum, row) => sum + row['Pmax (N)'], 0) / angleData.length;
  //       const avgKIC = angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length;
  //       averages[angle] = { avgPmax, avgKIC, count: angleData.length };
  //     }
  //   });
  //   return averages;
  // };

  const removeTableRow = (index: number) => {
    setTableData(tableData.filter((_, i) => i !== index));
  };

  const updateTableData = (index: number, field: keyof TableData, value: string | number) => {
    const newData = [...tableData];
    newData[index] = { ...newData[index], [field]: value };
    setTableData(newData);
  };

  const addCODRow = () => {
    setCodData([...codData, { 'Yük (kN)': 0, 'COD (mm)': 0 }]);
  };

  const removeCODRow = (index: number) => {
    setCodData(codData.filter((_, i) => i !== index));
  };

  const updateCODData = (index: number, field: keyof CODData, value: number) => {
    const newData = [...codData];
    newData[index] = { ...newData[index], [field]: value };
    setCodData(newData);
  };

  const exportCSV = (data: TableData[] | CODData[], filename: string) => {
    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleSCBCSVUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim());

        // Validate headers
        const expectedHeaders = ['SCB Numuneleri', 'Pmax (N)', 'KIC (MPa√m)'];
        const isValidFormat = expectedHeaders.every(header => headers.includes(header));

        if (!isValidFormat) {
          alert('CSV formatı hatalı! Başlıklar: SCB Numuneleri, Pmax (N), KIC (MPa√m) olmalıdır.');
          return;
        }

        const newData: TableData[] = [];
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',').map(v => v.trim());
          if (values.length >= 3) {
            newData.push({
              'SCB Numuneleri': values[0],
              'Pmax (N)': parseFloat(values[1]) || 0,
              'KIC (MPa√m)': parseFloat(values[2]) || 0,
            });
          }
        }

        setTableData(newData);
        setShowSCBUploadModal(false);
        alert(`${newData.length} satır başarıyla yüklendi!`);
      } catch {
        alert('CSV dosyası okunurken hata oluştu!');
      }
    };
    reader.readAsText(file);
    event.target.value = ''; // Reset input
  };

  const handleCODCSVUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim());

        // Validate headers
        const expectedHeaders = ['Yük (kN)', 'COD (mm)'];
        const isValidFormat = expectedHeaders.every(header => headers.includes(header));

        if (!isValidFormat) {
          alert('CSV formatı hatalı! Başlıklar: Yük (kN), COD (mm) olmalıdır.');
          return;
        }

        const newData: CODData[] = [];
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',').map(v => v.trim());
          if (values.length >= 2) {
            newData.push({
              'Yük (kN)': parseFloat(values[0]) || 0,
              'COD (mm)': parseFloat(values[1]) || 0,
            });
          }
        }

        setCodData(newData);
        setShowCODUploadModal(false);
        alert(`${newData.length} satır başarıyla yüklendi!`);
      } catch {
        alert('CSV dosyası okunurken hata oluştu!');
      }
    };
    reader.readAsText(file);
    event.target.value = ''; // Reset input
  };

  return (
    <div className="space-y-8">
      {/* SCB Test Results */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-slate-800 flex items-center">
            <div className="p-3 bg-blue-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            SCB Kırılma Tokluğu Test Sonuçları
          </h2>
          <div className="flex space-x-3">
            <button
              onClick={addTableRow}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
            >
              <Plus className="h-4 w-4" />
              <span>Satır Ekle</span>
            </button>
            <button
              onClick={() => setShowSCBUploadModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
            >
              <Upload className="h-4 w-4" />
              <span>CSV Yükle</span>
            </button>
            <button
              onClick={() => exportCSV(tableData, 'scb-test-results.csv')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
            >
              <Download className="h-4 w-4" />
              <span>CSV İndir</span>
            </button>
          </div>
        </div>

        <DataTable
          data={tableData}
          onUpdate={updateTableData}
          onRemove={removeTableRow}
        />

        <div className="mt-4 flex space-x-2">
          <button
            onClick={() => setShowKICChart(!showKICChart)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
          >
            {showKICChart ? 'Grafiği Gizle' : 'KIC Grafiğini Oluştur'}
          </button>
        </div>

        {showKICChart && (
          <div className="mt-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">KIC Değerleri Grafiği (Pmax vs KIC)</h3>
            <ScatterChart data={tableData} />
          </div>
        )}
      </div>

      {/* Load-COD Curve */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-slate-800 flex items-center">
            <div className="p-3 bg-green-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            Yük-COD Eğrisi Görselleştirme
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={addCODRow}
              className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
            >
              <Plus className="h-4 w-4" />
              <span>Satır Ekle</span>
            </button>
            <button
              onClick={() => setShowCODUploadModal(true)}
              className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
            >
              <Upload className="h-4 w-4" />
              <span>CSV Yükle</span>
            </button>
            <button
              onClick={() => exportCSV(codData, 'load-cod-data.csv')}
              className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Download className="h-4 w-4" />
              <span>CSV İndir</span>
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                  Yük (kN)
                </th>
                <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                  COD (mm)
                </th>
                <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {codData.map((row, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      step="0.01"
                      value={row['Yük (kN)']}
                      onChange={(e) => updateCODData(index, 'Yük (kN)', parseFloat(e.target.value) || 0)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      step="0.001"
                      value={row['COD (mm)']}
                      onChange={(e) => updateCODData(index, 'COD (mm)', parseFloat(e.target.value) || 0)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => removeCODRow(index)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-4">
          <button
            onClick={() => setShowCODChart(!showCODChart)}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            {showCODChart ? 'Grafiği Gizle' : 'Yük-COD Eğrisini Oluştur'}
          </button>
        </div>

        {showCODChart && (
          <div className="mt-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Tipik Yük-COD Eğrisi</h3>
            <LineChart data={codData} />
          </div>
        )}
      </div>

      {/* SCB CSV Upload Modal */}
      {showSCBUploadModal && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          onClick={() => setShowSCBUploadModal(false)}
        >
          {/* Blur background overlay */}
          <div
            className="absolute inset-0 bg-gray-200 bg-opacity-60"
            style={{
              backdropFilter: 'blur(4px)',
              WebkitBackdropFilter: 'blur(4px)'
            }}
          ></div>
          {/* Modal content */}
          <div
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900 flex items-center">
                <Info className="h-5 w-5 mr-2 text-blue-600" />
                SCB CSV Dosyası Yükleme
              </h3>
              <button
                onClick={() => setShowSCBUploadModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-base font-semibold text-gray-900 mb-3">
                CSV dosyanız aşağıdaki formatta olmalıdır:
              </p>
              <div className="bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono">
                <div className="font-bold text-gray-900 mb-1">SCB Numuneleri,Pmax (N),KIC (MPa√m)</div>
                <div className="text-gray-800">β=0°,370,0.16</div>
                <div className="text-gray-800">β=30°,720,0.31</div>
                <div className="text-gray-800">β=45°,450,0.20</div>
                <div className="text-gray-600">...</div>
              </div>
              <div className="text-sm font-medium text-gray-800 mt-3 space-y-1">
                <div>• İlk satır başlık satırı olmalıdır</div>
                <div>• Beta açıları: β=0°, β=15°, β=30°, β=45°, β=60°, β=75°, β=90°</div>
                <div>• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır</div>
              </div>
            </div>

            <div className="flex space-x-3">
              <label className="flex-1">
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleSCBCSVUpload}
                  className="hidden"
                />
                <div className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center">
                  Dosya Seç ve Yükle
                </div>
              </label>
              <button
                onClick={() => setShowSCBUploadModal(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                İptal
              </button>
            </div>
          </div>
        </div>
      )}

      {/* COD CSV Upload Modal */}
      {showCODUploadModal && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          onClick={() => setShowCODUploadModal(false)}
        >
          {/* Blur background overlay */}
          <div
            className="absolute inset-0 bg-gray-200 bg-opacity-60"
            style={{
              backdropFilter: 'blur(4px)',
              WebkitBackdropFilter: 'blur(4px)'
            }}
          ></div>
          {/* Modal content */}
          <div
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900 flex items-center">
                <Info className="h-5 w-5 mr-2 text-blue-600" />
                COD CSV Dosyası Yükleme
              </h3>
              <button
                onClick={() => setShowCODUploadModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-base font-semibold text-gray-900 mb-3">
                CSV dosyanız aşağıdaki formatta olmalıdır:
              </p>
              <div className="bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono">
                <div className="font-bold text-gray-900 mb-1">Yük (kN),COD (mm)</div>
                <div className="text-gray-800">0.00,0.00</div>
                <div className="text-gray-800">0.05,0.06</div>
                <div className="text-gray-800">0.10,0.12</div>
                <div className="text-gray-600">...</div>
              </div>
              <div className="text-sm font-medium text-gray-800 mt-3 space-y-1">
                <div>• İlk satır başlık satırı olmalıdır</div>
                <div>• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır</div>
                <div>• Yük değerleri kN cinsinden, COD değerleri mm cinsinden</div>
              </div>
            </div>

            <div className="flex space-x-3">
              <label className="flex-1">
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleCODCSVUpload}
                  className="hidden"
                />
                <div className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center">
                  Dosya Seç ve Yükle
                </div>
              </label>
              <button
                onClick={() => setShowCODUploadModal(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                İptal
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
