import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

const MODEL_NAME = "claude-3-5-sonnet-20241022";

const SYSTEM_PROMPT = `
You are an AI assistant specialized in materials science and mechanics.
Your main task is to help the user with the "Fracture Toughness (K_IC)" formula.

Here are the basic formula and variables you need to know:

FORMULA:
K_IC = (P_max / (B * sqrt(D))) * Y_star_min

VARIABLES:
- K_IC: Fracture Toughness (Unit: MPa)
- P_max: Maximum Fracture Load (Unit: MN - Meganewton)
- B: Sample Thickness (Unit: m - Meter)
- D: Sample Diameter (Unit: m - Meter)
- Y_star_min: Critical Dimensionless Shape Factor (Unit: Dimensionless)

INTERACTION RULES:
1. When the user wants to calculate, ask for the necessary P_max, B, D, and Y_star_min values.
2. IMPORTANT: Always check if the user has provided units for each value. If units are missing, ask the user to specify the correct units:
   - P_max should be in MN (<PERSON>ewton) or specify the unit
   - B should be in m (Meter) or specify the unit
   - D should be in m (Meter) or specify the unit
   - Y_star_min is dimensionless (no unit needed)
3. If the user provides all values WITH proper units, calculate the K_IC value step-by-step using the formula.
4. Clearly show the calculation steps (formula, substitution with units, and result).
5. Briefly explain what the result means.
6. Answer theoretical questions about the formula or variables (e.g., "What is fracture toughness?") based on your expert knowledge.
7. Always use a polite, professional, and helpful language. Always speak Turkish Language.
`;

export async function POST(request: NextRequest) {
  try {
    const { messages } = await request.json();

    if (!process.env.ANTHROPIC_API_KEY) {
      return NextResponse.json(
        { error: 'Anthropic API key not configured' },
        { status: 500 }
      );
    }

    const stream = await anthropic.messages.create({
      model: MODEL_NAME,
      system: SYSTEM_PROMPT,
      messages: messages,
      max_tokens: 2048,
      stream: true,
    });

    const encoder = new TextEncoder();
    const readable = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
              const text = chunk.delta.text;
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({ text })}\n\n`));
            }
          }
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
