# Kı<PERSON><PERSON>lma Tokluğu Asistanı

Yapay Zeka Destekli Kırılma Tokluğu Hesaplama ve Laboratuvar Veri Analizi Uygulaması

## Özellikler

- 🤖 **AI Destekli Sohbet Asistanı**: Kır<PERSON>lma tokluğu hesaplamaları ve teorik sorular için Claude AI entegrasyonu
- 📊 **Laboratuvar Veri Analizi**: SCB test sonuçlarının görselleştirilmesi ve analizi
- 📈 **Grafik Görselleştirme**: Scatter plot ve line chart desteği
- 📋 **Dinamik Tablo Düzenleme**: Verileri kolayca ekleyip düzenleyebilme
- 💾 **CSV Export/Import**: Verileri CSV formatında dışa aktarma
- 📱 **Responsive Tasarım**: Mobil ve masaüstü uyumlu modern arayüz

## Teknoloji Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Chart.js, React Chart.js 2
- **AI**: Anthropic Claude API
- **Icons**: Lucide React
- **Deployment**: Vercel

## Kurulum

1. Projeyi klonlayın:
```bash
git clone <repository-url>
cd fracture-app
```

2. Bağımlılıkları yükleyin:
```bash
npm install
```

3. Environment variables'ı ayarlayın:
```bash
cp .env.local.example .env.local
```

`.env.local` dosyasında `ANTHROPIC_API_KEY` değerini kendi API anahtarınızla değiştirin.

4. Development server'ı başlatın:
```bash
npm run dev
```

5. Tarayıcınızda [http://localhost:3000](http://localhost:3000) adresini açın.

## Vercel'de Deploy Etme

1. Vercel hesabınızda yeni bir proje oluşturun
2. GitHub repository'nizi bağlayın
3. Environment Variables bölümünde `ANTHROPIC_API_KEY` ekleyin
4. Deploy butonuna tıklayın

## Kullanım

### Kırılma Tokluğu Asistanı
- Sol sekmede AI asistanı ile sohbet edebilirsiniz
- Kırılma tokluğu formülü: `K_IC = (P_max / (B * sqrt(D))) * Y_star_min`
- Hesaplama için gerekli değerleri girin veya teorik sorular sorun

### Laboratuvar Veri Analizi
- Sağ sekmede SCB test sonuçlarını girebilirsiniz
- Verileri düzenleyebilir, yeni satırlar ekleyebilirsiniz
- Grafikleri oluşturarak sonuçları görselleştirebilirsiniz
- Yük-COD eğrisi verilerini de analiz edebilirsiniz

## API Endpoints

- `POST /api/chat` - AI sohbet endpoint'i (streaming response)

## Lisans

MIT License
