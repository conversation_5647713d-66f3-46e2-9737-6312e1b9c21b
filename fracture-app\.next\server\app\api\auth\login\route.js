(()=>{var e={};e.id=758,e.ids=[758],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5936:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>u});var n=r(6559),a=r(8088),o=r(7719),i=r(2190);async function u(e){try{let{username:t,password:r}=await e.json(),s=process.env.AUTH_USERNAME,n=process.env.AUTH_PASSWORD;if(!s||!n)return i.NextResponse.json({error:"Authentication not configured"},{status:500});if(t!==s||r!==n)return i.NextResponse.json({error:"Invalid credentials"},{status:401});{let e=i.NextResponse.json({success:!0});return e.cookies.set("auth-token","authenticated",{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}),e}}catch{return i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\fracture-main\\fracture-main\\fracture-app\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(5936));module.exports=s})();