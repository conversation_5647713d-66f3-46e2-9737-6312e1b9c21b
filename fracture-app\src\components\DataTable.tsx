'use client';

import { Trash2 } from 'lucide-react';

interface TableData {
  'SCB Numuneleri': string;
  'Pmax (N)': number;
  'KIC (MPa√m)': number;
}

interface DataTableProps {
  data: TableData[];
  onUpdate: (index: number, field: keyof TableData, value: string | number) => void;
  onRemove: (index: number) => void;
}

// Available beta angles
const BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];

export default function DataTable({ data, onUpdate, onRemove }: DataTableProps) {
  // Calculate averages for each beta angle dynamically
  const calculateAverages = () => {
    const averages: Record<string, { avgPmax: number; avgKIC: number; count: number }> = {};

    BETA_ANGLES.forEach(angle => {
      const angleData = data.filter(row => row['SCB Numuneleri'] === angle);
      if (angleData.length > 0) {
        const avgPmax = angleData.reduce((sum, row) => sum + row['Pmax (N)'], 0) / angleData.length;
        const avgKIC = angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length;
        averages[angle] = { avgPmax, avgKIC, count: angleData.length };
      }
    });

    return averages;
  };

  const averages = calculateAverages();

  // Define colors for different beta angles
  const getBetaColor = (angle: string) => {
    const colorMap: Record<string, string> = {
      'β=0°': 'bg-blue-50 text-blue-800',
      'β=15°': 'bg-green-50 text-green-800',
      'β=30°': 'bg-red-50 text-red-800',
      'β=45°': 'bg-yellow-50 text-yellow-800',
      'β=60°': 'bg-purple-50 text-purple-800',
      'β=75°': 'bg-pink-50 text-pink-800',
      'β=90°': 'bg-indigo-50 text-indigo-800',
    };
    return colorMap[angle] || 'bg-gray-50 text-gray-800';
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
              SCB Numuneleri
            </th>
            <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
              Pmax (N)
            </th>
            <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
              KIC (MPa√m)
            </th>
            <th className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
              İşlemler
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <select
                  value={row['SCB Numuneleri']}
                  onChange={(e) => onUpdate(index, 'SCB Numuneleri', e.target.value)}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                >
                  {BETA_ANGLES.map(angle => (
                    <option key={angle} value={angle}>{angle}</option>
                  ))}
                </select>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="number"
                  value={row['Pmax (N)']}
                  onChange={(e) => onUpdate(index, 'Pmax (N)', parseFloat(e.target.value) || 0)}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="number"
                  step="0.01"
                  value={row['KIC (MPa√m)']}
                  onChange={(e) => onUpdate(index, 'KIC (MPa√m)', parseFloat(e.target.value) || 0)}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <button
                  onClick={() => onRemove(index)}
                  className="text-red-600 hover:text-red-900"
                  title="Satırı Sil"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </td>
            </tr>
          ))}

          {/* Average section header - only show if there are averages */}
          {Object.keys(averages).length > 0 && (
            <tr className="bg-gray-100 border-t-2 border-gray-300">
              <td className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                ORTALAMA DEĞERLERİ
              </td>
              <td className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                Pmax (N)
              </td>
              <td className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                KIC (MPa√m)
              </td>
              <td className="px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider">
                İşlemler
              </td>
            </tr>
          )}

          {/* Dynamic Average rows for each beta angle */}
          {Object.entries(averages).map(([angle, avg]) => (
            <tr key={`avg-${angle}`} className={`font-semibold ${getBetaColor(angle)}`}>
              <td className="px-6 py-4 whitespace-nowrap">
                {angle} ORTALAMA ({avg.count} numune)
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {avg.avgPmax.toFixed(1)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {avg.avgKIC.toFixed(2)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                -
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {data.length === 0 && (
        <div className="text-center py-8 text-gray-700">
          <p className="text-lg font-medium">Henüz veri bulunmuyor. &quot;Satır Ekle&quot; butonunu kullanarak veri ekleyebilirsiniz.</p>
        </div>
      )}
    </div>
  );
}
