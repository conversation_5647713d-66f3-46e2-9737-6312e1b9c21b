'use client';

import { useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { Download } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface CODData {
  'Yük (kN)': number;
  'COD (mm)': number;
}

interface LineChartProps {
  data: CODData[];
}

export default function LineChart({ data }: LineChartProps) {
  const chartRef = useRef<ChartJS<'line'>>(null);

  const downloadChart = () => {
    const chart = chartRef.current;
    if (!chart) return;

    const canvas = chart.canvas;

    // Create a new canvas with white background
    const newCanvas = document.createElement('canvas');
    const newCtx = newCanvas.getContext('2d');
    newCanvas.width = canvas.width;
    newCanvas.height = canvas.height;

    // Fill with white background
    if (newCtx) {
      newCtx.fillStyle = '#ffffff';
      newCtx.fillRect(0, 0, newCanvas.width, newCanvas.height);

      // Draw the original chart on top
      newCtx.drawImage(canvas, 0, 0);
    }

    const url = newCanvas.toDataURL('image/jpeg', 0.95);

    const link = document.createElement('a');
    link.download = 'load-cod-curve.jpg';
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Sort data by COD for proper line connection
  const sortedData = [...data].sort((a, b) => a['COD (mm)'] - b['COD (mm)']);

  const chartData = {
    labels: sortedData.map(row => row['COD (mm)'].toFixed(3)),
    datasets: [
      {
        label: 'Yük-COD Eğrisi',
        data: sortedData.map(row => row['Yük (kN)']),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: 'rgb(34, 197, 94)',
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.1,
        fill: true,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Tipik Yük-COD Eğrisi',
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: { dataIndex: number; parsed: { y: number } }) {
            const codValue = sortedData[context.dataIndex]['COD (mm)'];
            return `Yük: ${context.parsed.y} kN, COD: ${codValue} mm`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Çatlak Ağız Açıklığı Yer Değiştirme (COD - mm)',
          font: {
            size: 14,
            weight: 'bold' as const,
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Yük (kN)',
          font: {
            size: 14,
            weight: 'bold' as const,
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        beginAtZero: true,
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <button
          onClick={downloadChart}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
        >
          <Download className="h-4 w-4" />
          <span>Grafiği İndir</span>
        </button>
      </div>
      <div className="h-96 w-full">
        <Line ref={chartRef} data={chartData} options={options} />
      </div>
    </div>
  );
}
