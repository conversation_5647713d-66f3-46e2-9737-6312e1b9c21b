(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{893:(e,s,t)=>{Promise.resolve().then(t.bind(t,2664))},2664:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),l=t(2115),r=t(5695),i=t(6766),n=t(1007),c=t(9946);let o=(0,c.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),d=(0,c.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),u=(0,c.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function x(){let[e,s]=(0,l.useState)(""),[t,c]=(0,l.useState)(""),[x,m]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1),[b,f]=(0,l.useState)(""),y=(0,r.useRouter)(),j=async s=>{s.preventDefault(),p(!0),f("");try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})});if(s.ok)y.push("/"),y.refresh();else{let e=await s.json();f(e.error||"Giriş başarısız")}}catch(e){f("Bağlantı hatası")}finally{p(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-20 w-20 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-6",children:(0,a.jsx)(i.default,{src:"/demokrasilogo.png",alt:"Demokrasi \xdcniversitesi Logo",width:64,height:64,className:"object-contain"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-slate-800",children:"Kırılma Tokluğu Asistanı"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-slate-600",children:"Sisteme giriş yapmak i\xe7in bilgilerinizi girin"})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:j,children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-slate-700 mb-2",children:"Kullanıcı Adı"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-slate-400"})}),(0,a.jsx)("input",{id:"username",name:"username",type:"text",required:!0,value:e,onChange:e=>s(e.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500",placeholder:"Kullanıcı adınızı girin"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-700 mb-2",children:"Şifre"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(o,{className:"h-5 w-5 text-slate-400"})}),(0,a.jsx)("input",{id:"password",name:"password",type:x?"text":"password",required:!0,value:t,onChange:e=>c(e.target.value),className:"block w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500",placeholder:"Şifrenizi girin"}),(0,a.jsx)("button",{type:"button",onClick:()=>m(!x),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:x?(0,a.jsx)(d,{className:"h-5 w-5 text-slate-400 hover:text-slate-600"}):(0,a.jsx)(u,{className:"h-5 w-5 text-slate-400 hover:text-slate-600"})})]})]})]}),b&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-600",children:b})}),(0,a.jsx)("button",{type:"submit",disabled:h,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105",children:h?"Giriş yapılıyor...":"Giriş Yap"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"Demokrasi \xdcniversitesi - G\xfcvenli Erişim"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[750,441,684,358],()=>s(893)),_N_E=e.O()}]);