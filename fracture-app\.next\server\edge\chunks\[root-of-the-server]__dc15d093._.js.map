{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  // Skip authentication for API routes and static files\n  if (\n    request.nextUrl.pathname.startsWith('/api/') ||\n    request.nextUrl.pathname.startsWith('/_next/') ||\n    request.nextUrl.pathname.startsWith('/favicon.ico') ||\n    request.nextUrl.pathname.startsWith('/demokrasilogo.png')\n  ) {\n    return NextResponse.next();\n  }\n\n  // Check if user is authenticated\n  const isAuthenticated = request.cookies.get('auth-token')?.value === 'authenticated';\n\n  // If not authenticated and not on login page, redirect to login\n  if (!isAuthenticated && request.nextUrl.pathname !== '/login') {\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  // If authenticated and on login page, redirect to home\n  if (isAuthenticated && request.nextUrl.pathname === '/login') {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,sDAAsD;IACtD,IACE,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,uBACpC;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,iCAAiC;IACjC,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU;IAErE,gEAAgE;IAChE,IAAI,CAAC,mBAAmB,QAAQ,OAAO,CAAC,QAAQ,KAAK,UAAU;QAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,uDAAuD;IACvD,IAAI,mBAAmB,QAAQ,OAAO,CAAC,QAAQ,KAAK,UAAU;QAC5D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}