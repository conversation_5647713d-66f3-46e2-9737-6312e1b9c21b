{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/components/ChatAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Send, Bot, User, Loader2, Co<PERSON>, Check } from 'lucide-react';\n\ninterface Message {\n  role: 'user' | 'assistant';\n  content: string;\n}\n\ninterface ChatAssistantProps {\n  messages: Message[];\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n}\n\nexport default function ChatAssistant({ messages, setMessages }: ChatAssistantProps) {\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleQuickAction = (action: 'calculation' | 'theory' | 'variables') => {\n    let message = '';\n\n    switch (action) {\n      case 'calculation':\n        message = 'Kırılma tokluğu hesaplaması yapmak istiyorum. Hangi değerleri girmeliyim?';\n        break;\n      case 'theory':\n        message = 'Kırılma tokluğu nedir? Teorik bilgi verebilir misin?';\n        break;\n      case 'variables':\n        message = 'Pmax, B, D ve Y* değişkenlerinin ne anlama geldiğini açıklar mısın?';\n        break;\n    }\n\n    setInput(message);\n    // Otomatik olarak mesajı gönder\n    setTimeout(() => {\n      sendMessage();\n    }, 100);\n  };\n\n  const sendMessage = async () => {\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = { role: 'user', content: input };\n    const newMessages = [...messages, userMessage];\n    setMessages(newMessages);\n    setInput('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ messages: newMessages }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) throw new Error('No reader available');\n\n      let assistantMessage = '';\n      setMessages([...newMessages, { role: 'assistant', content: '' }]);\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        const chunk = new TextDecoder().decode(value);\n        const lines = chunk.split('\\n');\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') break;\n            \n            try {\n              const parsed = JSON.parse(data);\n              if (parsed.text) {\n                assistantMessage += parsed.text;\n                setMessages([...newMessages, { role: 'assistant', content: assistantMessage }]);\n              }\n            } catch {\n              // Ignore parsing errors\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      setMessages([...newMessages, { \n        role: 'assistant', \n        content: 'Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.' \n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const copyToClipboard = async (text: string, index: number) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedIndex(index);\n      setTimeout(() => setCopiedIndex(null), 2000); // Reset after 2 seconds\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 h-[calc(100vh-200px)] min-h-[500px] max-h-[800px] flex flex-col\">\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 md:p-6 space-y-4\">\n        {messages.length === 0 && (\n          <div className=\"text-center text-slate-700 mt-8\">\n            <div className=\"p-4 bg-blue-600 rounded-2xl w-16 h-16 mx-auto mb-6 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\">\n              <Bot className=\"h-8 w-8 text-white\" />\n            </div>\n            <p className=\"text-2xl font-bold text-slate-800\">\n              Kırılma Tokluğu Asistanına Hoş Geldiniz!\n            </p>\n            <p className=\"text-base mt-3 mb-8 font-medium text-slate-600\">\n              Hesaplama yapmak için değerleri girin veya teorik sorularınızı sorun.\n            </p>\n\n            {/* Quick Action Buttons */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 max-w-2xl mx-auto px-2 md:px-0\">\n              <button\n                onClick={() => handleQuickAction('calculation')}\n                className=\"flex flex-col items-center p-3 md:p-4 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 hover:border-green-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md\"\n              >\n                <div className=\"p-2 bg-green-600 rounded-lg mb-2\">\n                  <span className=\"text-white text-lg\">✅</span>\n                </div>\n                <span className=\"font-semibold text-slate-800 text-sm md:text-base\">Hesaplama Yap</span>\n                <span className=\"text-xs md:text-sm text-slate-600 mt-1\">KIC değeri hesapla</span>\n              </button>\n\n              <button\n                onClick={() => handleQuickAction('theory')}\n                className=\"flex flex-col items-center p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md\"\n              >\n                <div className=\"p-2 bg-blue-600 rounded-lg mb-2\">\n                  <span className=\"text-white text-lg\">📘</span>\n                </div>\n                <span className=\"font-semibold text-slate-800 text-sm md:text-base\">Teorik Bilgi</span>\n                <span className=\"text-xs md:text-sm text-slate-600 mt-1\">Kırılma tokluğu nedir?</span>\n              </button>\n\n              <button\n                onClick={() => handleQuickAction('variables')}\n                className=\"flex flex-col items-center p-3 md:p-4 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md\"\n              >\n                <div className=\"p-2 bg-purple-600 rounded-lg mb-2\">\n                  <span className=\"text-white text-lg\">ℹ️</span>\n                </div>\n                <span className=\"font-semibold text-slate-800 text-sm md:text-base\">Değişken Açıklamaları</span>\n                <span className=\"text-xs md:text-sm text-slate-600 mt-1\">Pmax, B, D, Y* nedir?</span>\n              </button>\n            </div>\n          </div>\n        )}\n\n        {messages.map((message, index) => (\n          <div\n            key={index}\n            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} group`}\n          >\n            <div\n              className={`max-w-[85%] md:max-w-[80%] rounded-xl px-3 md:px-4 py-2 md:py-3 relative shadow-sm ${\n                message.role === 'user'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-100 text-slate-800'\n              }`}\n            >\n              <div className=\"flex items-start space-x-2\">\n                {message.role === 'assistant' && (\n                  <Bot className=\"h-4 w-4 mt-0.5 text-blue-600\" />\n                )}\n                {message.role === 'user' && (\n                  <User className=\"h-4 w-4 mt-0.5 text-white\" />\n                )}\n                <div className=\"whitespace-pre-wrap flex-1\">{message.content}</div>\n              </div>\n\n              {/* Copy button */}\n              <button\n                onClick={() => copyToClipboard(message.content, index)}\n                className={`absolute top-2 right-2 p-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-105 ${\n                  message.role === 'user'\n                    ? 'hover:bg-blue-700 text-white'\n                    : 'hover:bg-gray-200 text-slate-600'\n                }`}\n                title=\"Metni kopyala\"\n              >\n                {copiedIndex === index ? (\n                  <Check className=\"h-3 w-3\" />\n                ) : (\n                  <Copy className=\"h-3 w-3\" />\n                )}\n              </button>\n            </div>\n          </div>\n        ))}\n\n        {isLoading && (\n          <div className=\"flex justify-start\">\n            <div className=\"bg-gray-100 rounded-xl px-4 py-3 shadow-sm\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-1 bg-blue-600 rounded-lg\">\n                  <Bot className=\"h-3 w-3 text-white\" />\n                </div>\n                <Loader2 className=\"h-4 w-4 animate-spin text-blue-600\" />\n                <span className=\"text-slate-600\">Yazıyor...</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"p-4 md:p-6 border-t border-gray-100\">\n        <div className=\"flex space-x-2 md:space-x-3\">\n          <textarea\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Değerleri girin veya bir soru sorun...\"\n            className=\"flex-1 resize-none border border-gray-200 rounded-xl px-3 md:px-4 py-2 md:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500 text-sm md:text-base\"\n            rows={2}\n            disabled={isLoading}\n          />\n          <button\n            onClick={sendMessage}\n            disabled={!input.trim() || isLoading}\n            className=\"bg-blue-600 text-white rounded-xl px-4 md:px-6 py-2 md:py-3 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAee,SAAS,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAsB;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QAEd,OAAQ;YACN,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;QACJ;QAEA,SAAS;QACT,gCAAgC;QAChC,WAAW;YACT;QACF,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAAE,MAAM;YAAQ,SAAS;QAAM;QAC5D,MAAM,cAAc;eAAI;YAAU;SAAY;QAC9C,YAAY;QACZ,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAY;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;YAE7B,IAAI,mBAAmB;YACvB,YAAY;mBAAI;gBAAa;oBAAE,MAAM;oBAAa,SAAS;gBAAG;aAAE;YAEhE,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,IAAI,cAAc,MAAM,CAAC;gBACvC,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;wBAEvB,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,IAAI,OAAO,IAAI,EAAE;gCACf,oBAAoB,OAAO,IAAI;gCAC/B,YAAY;uCAAI;oCAAa;wCAAE,MAAM;wCAAa,SAAS;oCAAiB;iCAAE;4BAChF;wBACF,EAAE,OAAM;wBACN,wBAAwB;wBAC1B;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;mBAAI;gBAAa;oBAC3B,MAAM;oBACN,SAAS;gBACX;aAAE;QACJ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,eAAe;YACf,WAAW,IAAM,eAAe,OAAO,OAAO,wBAAwB;QACxE,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAK9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAoD;;;;;;0DACpE,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAG3D,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAoD;;;;;;0DACpE,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAG3D,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAoD;;;;;;0DACpE,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;oBAMhE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,gBAAgB,MAAM,CAAC;sCAEpF,cAAA,8OAAC;gCACC,WAAW,CAAC,mFAAmF,EAC7F,QAAQ,IAAI,KAAK,SACb,2BACA,8BACJ;;kDAEF,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,KAAK,6BAChB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAEhB,QAAQ,IAAI,KAAK,wBAChB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAElB,8OAAC;gDAAI,WAAU;0DAA8B,QAAQ,OAAO;;;;;;;;;;;;kDAI9D,8OAAC;wCACC,SAAS,IAAM,gBAAgB,QAAQ,OAAO,EAAE;wCAChD,WAAW,CAAC,oHAAoH,EAC9H,QAAQ,IAAI,KAAK,SACb,iCACA,oCACJ;wCACF,OAAM;kDAEL,gBAAgB,sBACf,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;iEAEjB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAjCjB;;;;;oBAwCR,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;kCAMzC,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,YAAY;4BACZ,aAAY;4BACZ,WAAU;4BACV,MAAM;4BACN,UAAU;;;;;;sCAEZ,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,MAAM,IAAI,MAAM;4BAC3B,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/components/ScatterChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ScatterController,\n  TooltipItem,\n} from 'chart.js';\nimport { Scatter } from 'react-chartjs-2';\nimport { Download } from 'lucide-react';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ScatterController\n);\n\ninterface TableData {\n  'SCB Numuneleri': string;\n  'Pmax (N)': number;\n  'KIC (MPa√m)': number;\n}\n\ninterface ScatterChartProps {\n  data: TableData[];\n}\n\n// Available beta angles and their colors\nconst BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];\nconst BETA_COLORS = [\n  { bg: 'rgba(59, 130, 246, 0.7)', border: 'rgb(59, 130, 246)' }, // Blue\n  { bg: 'rgba(34, 197, 94, 0.7)', border: 'rgb(34, 197, 94)' },   // Green\n  { bg: 'rgba(239, 68, 68, 0.7)', border: 'rgb(239, 68, 68)' },   // Red\n  { bg: 'rgba(245, 158, 11, 0.7)', border: 'rgb(245, 158, 11)' }, // Yellow\n  { bg: 'rgba(168, 85, 247, 0.7)', border: 'rgb(168, 85, 247)' }, // Purple\n  { bg: 'rgba(236, 72, 153, 0.7)', border: 'rgb(236, 72, 153)' }, // Pink\n  { bg: 'rgba(99, 102, 241, 0.7)', border: 'rgb(99, 102, 241)' }, // Indigo\n];\n\nexport default function ScatterChart({ data }: ScatterChartProps) {\n  const chartRef = useRef<ChartJS<'scatter'>>(null);\n\n  const downloadChart = () => {\n    const chart = chartRef.current;\n    if (!chart) return;\n\n    const canvas = chart.canvas;\n\n    // Create a new canvas with white background\n    const newCanvas = document.createElement('canvas');\n    const newCtx = newCanvas.getContext('2d');\n    newCanvas.width = canvas.width;\n    newCanvas.height = canvas.height;\n\n    // Fill with white background\n    if (newCtx) {\n      newCtx.fillStyle = '#ffffff';\n      newCtx.fillRect(0, 0, newCanvas.width, newCanvas.height);\n\n      // Draw the original chart on top\n      newCtx.drawImage(canvas, 0, 0);\n    }\n\n    const url = newCanvas.toDataURL('image/jpeg', 0.95);\n\n    const link = document.createElement('a');\n    link.download = 'kic-scatter-chart.jpg';\n    link.href = url;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // Group data by beta angles and calculate averages\n  const groupedData = BETA_ANGLES.map((angle, index) => {\n    const angleData = data.filter(row => row['SCB Numuneleri'] === angle);\n    const average = angleData.length > 0\n      ? angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length\n      : 0;\n\n    return {\n      angle,\n      data: angleData,\n      average,\n      color: BETA_COLORS[index] || BETA_COLORS[0],\n      count: angleData.length\n    };\n  }).filter(group => group.count > 0); // Only include groups with data\n\n  const chartData = {\n    datasets: groupedData.map((group, index) => ({\n      label: `${group.angle} (${group.count} numune)`,\n      data: group.data.map(row => ({\n        x: row['Pmax (N)'],\n        y: row['KIC (MPa√m)']\n      })),\n      backgroundColor: group.color.bg,\n      borderColor: group.color.border,\n      pointRadius: 6,\n      pointHoverRadius: 8,\n      pointStyle: index % 2 === 0 ? 'circle' : 'cross', // Alternate between circle and cross\n    })),\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'SCB Kırılma Tokluğu Test Sonuçları',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: TooltipItem<'scatter'>) {\n            const label = context.dataset.label || '';\n            return `${label}: Pmax=${context.parsed.x}N, KIC=${context.parsed.y}MPa√m`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        display: true,\n        title: {\n          display: true,\n          text: 'Pmax (N)',\n          font: {\n            size: 14,\n            weight: 'bold' as const,\n          },\n        },\n        grid: {\n          display: true,\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n      },\n      y: {\n        display: true,\n        title: {\n          display: true,\n          text: 'KIC (MPa√m)',\n          font: {\n            size: 14,\n            weight: 'bold' as const,\n          },\n        },\n        grid: {\n          display: true,\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n      },\n    },\n    interaction: {\n      intersect: false,\n      mode: 'point' as const,\n    },\n  };\n\n  // Add average lines after chart is rendered\n  useEffect(() => {\n    const chart = chartRef.current;\n    if (!chart) return;\n\n    const ctx = chart.ctx;\n    const chartArea = chart.chartArea;\n\n    if (!chartArea) return;\n\n    // Draw average lines for each group\n    ctx.save();\n\n    groupedData.forEach((group, index) => {\n      if (group.average > 0) {\n        ctx.strokeStyle = group.color.border.replace('rgb', 'rgba').replace(')', ', 0.8)');\n        ctx.setLineDash([5, 5]);\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        const y = chart.scales.y.getPixelForValue(group.average);\n        ctx.moveTo(chartArea.left, y);\n        ctx.lineTo(chartArea.right, y);\n        ctx.stroke();\n\n        // Add text label\n        ctx.fillStyle = group.color.border;\n        ctx.font = '12px Arial';\n        const labelY = y - 5 - (index * 15); // Offset labels to avoid overlap\n        ctx.fillText(`${group.angle} Ort: ${group.average.toFixed(2)}`, chartArea.right - 120, labelY);\n      }\n    });\n\n    ctx.restore();\n  }, [groupedData]);\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-end\">\n        <button\n          onClick={downloadChart}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n        >\n          <Download className=\"h-4 w-4\" />\n          <span>Grafiği İndir</span>\n        </button>\n      </div>\n      <div className=\"h-96 w-full\">\n        <Scatter ref={chartRef} data={chartData} options={options} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAYA;AACA;AAhBA;;;;;;AAkBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,oBAAiB;AAanB,yCAAyC;AACzC,MAAM,cAAc;IAAC;IAAQ;IAAS;IAAS;IAAS;IAAS;IAAS;CAAQ;AAClF,MAAM,cAAc;IAClB;QAAE,IAAI;QAA2B,QAAQ;IAAoB;IAC7D;QAAE,IAAI;QAA0B,QAAQ;IAAmB;IAC3D;QAAE,IAAI;QAA0B,QAAQ;IAAmB;IAC3D;QAAE,IAAI;QAA2B,QAAQ;IAAoB;IAC7D;QAAE,IAAI;QAA2B,QAAQ;IAAoB;IAC7D;QAAE,IAAI;QAA2B,QAAQ;IAAoB;IAC7D;QAAE,IAAI;QAA2B,QAAQ;IAAoB;CAC9D;AAEc,SAAS,aAAa,EAAE,IAAI,EAAqB;IAC9D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE5C,MAAM,gBAAgB;QACpB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,SAAS,MAAM,MAAM;QAE3B,4CAA4C;QAC5C,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,MAAM,SAAS,UAAU,UAAU,CAAC;QACpC,UAAU,KAAK,GAAG,OAAO,KAAK;QAC9B,UAAU,MAAM,GAAG,OAAO,MAAM;QAEhC,6BAA6B;QAC7B,IAAI,QAAQ;YACV,OAAO,SAAS,GAAG;YACnB,OAAO,QAAQ,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,UAAU,MAAM;YAEvD,iCAAiC;YACjC,OAAO,SAAS,CAAC,QAAQ,GAAG;QAC9B;QAEA,MAAM,MAAM,UAAU,SAAS,CAAC,cAAc;QAE9C,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,mDAAmD;IACnD,MAAM,cAAc,YAAY,GAAG,CAAC,CAAC,OAAO;QAC1C,MAAM,YAAY,KAAK,MAAM,CAAC,CAAA,MAAO,GAAG,CAAC,iBAAiB,KAAK;QAC/D,MAAM,UAAU,UAAU,MAAM,GAAG,IAC/B,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,GAAG,CAAC,cAAc,EAAE,KAAK,UAAU,MAAM,GAC9E;QAEJ,OAAO;YACL;YACA,MAAM;YACN;YACA,OAAO,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,EAAE;YAC3C,OAAO,UAAU,MAAM;QACzB;IACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG,IAAI,gCAAgC;IAErE,MAAM,YAAY;QAChB,UAAU,YAAY,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBAC3C,OAAO,GAAG,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,QAAQ,CAAC;gBAC/C,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC3B,GAAG,GAAG,CAAC,WAAW;wBAClB,GAAG,GAAG,CAAC,cAAc;oBACvB,CAAC;gBACD,iBAAiB,MAAM,KAAK,CAAC,EAAE;gBAC/B,aAAa,MAAM,KAAK,CAAC,MAAM;gBAC/B,aAAa;gBACb,kBAAkB;gBAClB,YAAY,QAAQ,MAAM,IAAI,WAAW;YAC3C,CAAC;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAA+B;wBAC7C,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,IAAI;wBACvC,OAAO,GAAG,MAAM,OAAO,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC5E;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,MAAM;oBACJ,SAAS;oBACT,OAAO;gBACT;YACF;YACA,GAAG;gBACD,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,MAAM;oBACJ,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QACA,aAAa;YACX,WAAW;YACX,MAAM;QACR;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,MAAM,MAAM,GAAG;QACrB,MAAM,YAAY,MAAM,SAAS;QAEjC,IAAI,CAAC,WAAW;QAEhB,oCAAoC;QACpC,IAAI,IAAI;QAER,YAAY,OAAO,CAAC,CAAC,OAAO;YAC1B,IAAI,MAAM,OAAO,GAAG,GAAG;gBACrB,IAAI,WAAW,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,QAAQ,OAAO,CAAC,KAAK;gBACzE,IAAI,WAAW,CAAC;oBAAC;oBAAG;iBAAE;gBACtB,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS;gBACb,MAAM,IAAI,MAAM,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,OAAO;gBACvD,IAAI,MAAM,CAAC,UAAU,IAAI,EAAE;gBAC3B,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE;gBAC5B,IAAI,MAAM;gBAEV,iBAAiB;gBACjB,IAAI,SAAS,GAAG,MAAM,KAAK,CAAC,MAAM;gBAClC,IAAI,IAAI,GAAG;gBACX,MAAM,SAAS,IAAI,IAAK,QAAQ,IAAK,iCAAiC;gBACtE,IAAI,QAAQ,CAAC,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,KAAK;YACzF;QACF;QAEA,IAAI,OAAO;IACb,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sJAAA,CAAA,UAAO;oBAAC,KAAK;oBAAU,MAAM;oBAAW,SAAS;;;;;;;;;;;;;;;;;AAI1D", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/components/LineChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { Download } from 'lucide-react';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface CODData {\n  'Yük (kN)': number;\n  'COD (mm)': number;\n}\n\ninterface LineChartProps {\n  data: CODData[];\n}\n\nexport default function LineChart({ data }: LineChartProps) {\n  const chartRef = useRef<ChartJS<'line'>>(null);\n\n  const downloadChart = () => {\n    const chart = chartRef.current;\n    if (!chart) return;\n\n    const canvas = chart.canvas;\n\n    // Create a new canvas with white background\n    const newCanvas = document.createElement('canvas');\n    const newCtx = newCanvas.getContext('2d');\n    newCanvas.width = canvas.width;\n    newCanvas.height = canvas.height;\n\n    // Fill with white background\n    if (newCtx) {\n      newCtx.fillStyle = '#ffffff';\n      newCtx.fillRect(0, 0, newCanvas.width, newCanvas.height);\n\n      // Draw the original chart on top\n      newCtx.drawImage(canvas, 0, 0);\n    }\n\n    const url = newCanvas.toDataURL('image/jpeg', 0.95);\n\n    const link = document.createElement('a');\n    link.download = 'load-cod-curve.jpg';\n    link.href = url;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // Sort data by COD for proper line connection\n  const sortedData = [...data].sort((a, b) => a['COD (mm)'] - b['COD (mm)']);\n\n  const chartData = {\n    labels: sortedData.map(row => row['COD (mm)'].toFixed(3)),\n    datasets: [\n      {\n        label: 'Yük-COD Eğrisi',\n        data: sortedData.map(row => row['Yük (kN)']),\n        borderColor: 'rgb(34, 197, 94)',\n        backgroundColor: 'rgba(34, 197, 94, 0.1)',\n        borderWidth: 2,\n        pointBackgroundColor: 'rgb(34, 197, 94)',\n        pointBorderColor: 'rgb(34, 197, 94)',\n        pointRadius: 4,\n        pointHoverRadius: 6,\n        tension: 0.1,\n        fill: true,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: 'Tipik Yük-COD Eğrisi',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: { dataIndex: number; parsed: { y: number } }) {\n            const codValue = sortedData[context.dataIndex]['COD (mm)'];\n            return `Yük: ${context.parsed.y} kN, COD: ${codValue} mm`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        display: true,\n        title: {\n          display: true,\n          text: 'Çatlak Ağız Açıklığı Yer Değiştirme (COD - mm)',\n          font: {\n            size: 14,\n            weight: 'bold' as const,\n          },\n        },\n        grid: {\n          display: true,\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n      },\n      y: {\n        display: true,\n        title: {\n          display: true,\n          text: 'Yük (kN)',\n          font: {\n            size: 14,\n            weight: 'bold' as const,\n          },\n        },\n        grid: {\n          display: true,\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n        beginAtZero: true,\n      },\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index' as const,\n    },\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-end\">\n        <button\n          onClick={downloadChart}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n        >\n          <Download className=\"h-4 w-4\" />\n          <span>Grafiği İndir</span>\n        </button>\n      </div>\n      <div className=\"h-96 w-full\">\n        <Line ref={chartRef} data={chartData} options={options} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAUA;AACA;AAdA;;;;;;AAgBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAYO,SAAS,UAAU,EAAE,IAAI,EAAkB;IACxD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IAEzC,MAAM,gBAAgB;QACpB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,SAAS,MAAM,MAAM;QAE3B,4CAA4C;QAC5C,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,MAAM,SAAS,UAAU,UAAU,CAAC;QACpC,UAAU,KAAK,GAAG,OAAO,KAAK;QAC9B,UAAU,MAAM,GAAG,OAAO,MAAM;QAEhC,6BAA6B;QAC7B,IAAI,QAAQ;YACV,OAAO,SAAS,GAAG;YACnB,OAAO,QAAQ,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,UAAU,MAAM;YAEvD,iCAAiC;YACjC,OAAO,SAAS,CAAC,QAAQ,GAAG;QAC9B;QAEA,MAAM,MAAM,UAAU,SAAS,CAAC,cAAc;QAE9C,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,8CAA8C;IAC9C,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW;IAEzE,MAAM,YAAY;QAChB,QAAQ,WAAW,GAAG,CAAC,CAAA,MAAO,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;QACtD,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,WAAW,GAAG,CAAC,CAAA,MAAO,GAAG,CAAC,WAAW;gBAC3C,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,sBAAsB;gBACtB,kBAAkB;gBAClB,aAAa;gBACb,kBAAkB;gBAClB,SAAS;gBACT,MAAM;YACR;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAqD;wBACnE,MAAM,WAAW,UAAU,CAAC,QAAQ,SAAS,CAAC,CAAC,WAAW;wBAC1D,OAAO,CAAC,KAAK,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,UAAU,EAAE,SAAS,GAAG,CAAC;oBAC3D;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,MAAM;oBACJ,SAAS;oBACT,OAAO;gBACT;YACF;YACA,GAAG;gBACD,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,MAAM;oBACJ,SAAS;oBACT,OAAO;gBACT;gBACA,aAAa;YACf;QACF;QACA,aAAa;YACX,WAAW;YACX,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sJAAA,CAAA,OAAI;oBAAC,KAAK;oBAAU,MAAM;oBAAW,SAAS;;;;;;;;;;;;;;;;;AAIvD", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/components/DataTable.tsx"], "sourcesContent": ["'use client';\n\nimport { Trash2 } from 'lucide-react';\n\ninterface TableData {\n  'SCB Numuneleri': string;\n  'Pmax (N)': number;\n  'KIC (MPa√m)': number;\n}\n\ninterface DataTableProps {\n  data: TableData[];\n  onUpdate: (index: number, field: keyof TableData, value: string | number) => void;\n  onRemove: (index: number) => void;\n}\n\n// Available beta angles\nconst BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];\n\nexport default function DataTable({ data, onUpdate, onRemove }: DataTableProps) {\n  // Calculate averages for each beta angle dynamically\n  const calculateAverages = () => {\n    const averages: Record<string, { avgPmax: number; avgKIC: number; count: number }> = {};\n\n    BETA_ANGLES.forEach(angle => {\n      const angleData = data.filter(row => row['SCB Numuneleri'] === angle);\n      if (angleData.length > 0) {\n        const avgPmax = angleData.reduce((sum, row) => sum + row['Pmax (N)'], 0) / angleData.length;\n        const avgKIC = angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length;\n        averages[angle] = { avgPmax, avgKIC, count: angleData.length };\n      }\n    });\n\n    return averages;\n  };\n\n  const averages = calculateAverages();\n\n  // Define colors for different beta angles\n  const getBetaColor = (angle: string) => {\n    const colorMap: Record<string, string> = {\n      'β=0°': 'bg-blue-50 text-blue-800',\n      'β=15°': 'bg-green-50 text-green-800',\n      'β=30°': 'bg-red-50 text-red-800',\n      'β=45°': 'bg-yellow-50 text-yellow-800',\n      'β=60°': 'bg-purple-50 text-purple-800',\n      'β=75°': 'bg-pink-50 text-pink-800',\n      'β=90°': 'bg-indigo-50 text-indigo-800',\n    };\n    return colorMap[angle] || 'bg-gray-50 text-gray-800';\n  };\n\n  return (\n    <div className=\"overflow-x-auto\">\n      <table className=\"min-w-full divide-y divide-gray-200\">\n        <thead className=\"bg-gray-50\">\n          <tr>\n            <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n              SCB Numuneleri\n            </th>\n            <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n              Pmax (N)\n            </th>\n            <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n              KIC (MPa√m)\n            </th>\n            <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n              İşlemler\n            </th>\n          </tr>\n        </thead>\n        <tbody className=\"bg-white divide-y divide-gray-200\">\n          {data.map((row, index) => (\n            <tr key={index} className=\"hover:bg-gray-50\">\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                <select\n                  value={row['SCB Numuneleri']}\n                  onChange={(e) => onUpdate(index, 'SCB Numuneleri', e.target.value)}\n                  className=\"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium\"\n                >\n                  {BETA_ANGLES.map(angle => (\n                    <option key={angle} value={angle}>{angle}</option>\n                  ))}\n                </select>\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                <input\n                  type=\"number\"\n                  value={row['Pmax (N)']}\n                  onChange={(e) => onUpdate(index, 'Pmax (N)', parseFloat(e.target.value) || 0)}\n                  className=\"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium\"\n                />\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={row['KIC (MPa√m)']}\n                  onChange={(e) => onUpdate(index, 'KIC (MPa√m)', parseFloat(e.target.value) || 0)}\n                  className=\"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium\"\n                />\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                <button\n                  onClick={() => onRemove(index)}\n                  className=\"text-red-600 hover:text-red-900\"\n                  title=\"Satırı Sil\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </td>\n            </tr>\n          ))}\n\n          {/* Average section header - only show if there are averages */}\n          {Object.keys(averages).length > 0 && (\n            <tr className=\"bg-gray-100 border-t-2 border-gray-300\">\n              <td className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                ORTALAMA DEĞERLERİ\n              </td>\n              <td className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                Pmax (N)\n              </td>\n              <td className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                KIC (MPa√m)\n              </td>\n              <td className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                İşlemler\n              </td>\n            </tr>\n          )}\n\n          {/* Dynamic Average rows for each beta angle */}\n          {Object.entries(averages).map(([angle, avg]) => (\n            <tr key={`avg-${angle}`} className={`font-semibold ${getBetaColor(angle)}`}>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                {angle} ORTALAMA ({avg.count} numune)\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                {avg.avgPmax.toFixed(1)}\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                {avg.avgKIC.toFixed(2)}\n              </td>\n              <td className=\"px-6 py-4 whitespace-nowrap\">\n                -\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n      \n      {data.length === 0 && (\n        <div className=\"text-center py-8 text-gray-700\">\n          <p className=\"text-lg font-medium\">Henüz veri bulunmuyor. &quot;Satır Ekle&quot; butonunu kullanarak veri ekleyebilirsiniz.</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,wBAAwB;AACxB,MAAM,cAAc;IAAC;IAAQ;IAAS;IAAS;IAAS;IAAS;IAAS;CAAQ;AAEnE,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAkB;IAC5E,qDAAqD;IACrD,MAAM,oBAAoB;QACxB,MAAM,WAA+E,CAAC;QAEtF,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,YAAY,KAAK,MAAM,CAAC,CAAA,MAAO,GAAG,CAAC,iBAAiB,KAAK;YAC/D,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,UAAU,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,GAAG,CAAC,WAAW,EAAE,KAAK,UAAU,MAAM;gBAC3F,MAAM,SAAS,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,GAAG,CAAC,cAAc,EAAE,KAAK,UAAU,MAAM;gBAC7F,QAAQ,CAAC,MAAM,GAAG;oBAAE;oBAAS;oBAAQ,OAAO,UAAU,MAAM;gBAAC;YAC/D;QACF;QAEA,OAAO;IACT;IAEA,MAAM,WAAW;IAEjB,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,MAAM,WAAmC;YACvC,QAAQ;YACR,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;QACX;QACA,OAAO,QAAQ,CAAC,MAAM,IAAI;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+E;;;;;;8CAG7F,8OAAC;oCAAG,WAAU;8CAA+E;;;;;;8CAG7F,8OAAC;oCAAG,WAAU;8CAA+E;;;;;;8CAG7F,8OAAC;oCAAG,WAAU;8CAA+E;;;;;;;;;;;;;;;;;kCAKjG,8OAAC;wBAAM,WAAU;;4BACd,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;oCAAe,WAAU;;sDACxB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,OAAO,GAAG,CAAC,iBAAiB;gDAC5B,UAAU,CAAC,IAAM,SAAS,OAAO,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjE,WAAU;0DAET,YAAY,GAAG,CAAC,CAAA,sBACf,8OAAC;wDAAmB,OAAO;kEAAQ;uDAAtB;;;;;;;;;;;;;;;sDAInB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,MAAK;gDACL,OAAO,GAAG,CAAC,WAAW;gDACtB,UAAU,CAAC,IAAM,SAAS,OAAO,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC3E,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,GAAG,CAAC,cAAc;gDACzB,UAAU,CAAC,IAAM,SAAS,OAAO,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC9E,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,SAAS,IAAM,SAAS;gDACxB,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAnCf;;;;;4BA0CV,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBAC9B,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAA+E;;;;;;kDAG7F,8OAAC;wCAAG,WAAU;kDAA+E;;;;;;kDAG7F,8OAAC;wCAAG,WAAU;kDAA+E;;;;;;kDAG7F,8OAAC;wCAAG,WAAU;kDAA+E;;;;;;;;;;;;4BAOhG,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,iBACzC,8OAAC;oCAAwB,WAAW,CAAC,cAAc,EAAE,aAAa,QAAQ;;sDACxE,8OAAC;4CAAG,WAAU;;gDACX;gDAAM;gDAAY,IAAI,KAAK;gDAAC;;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;sDACX,IAAI,OAAO,CAAC,OAAO,CAAC;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDACX,IAAI,MAAM,CAAC,OAAO,CAAC;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;;mCAVrC,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;;YAkB5B,KAAK,MAAM,KAAK,mBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/components/DataAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { BarChart3, Download, Plus, Trash2, Upload, Info, X } from 'lucide-react';\nimport ScatterChart from './ScatterChart';\nimport LineChart from './LineChart';\nimport DataTable from './DataTable';\n\ninterface TableData {\n  'SCB Numuneleri': string;\n  'Pmax (N)': number;\n  'KIC (MPa√m)': number;\n}\n\ninterface CODData {\n  'Yük (kN)': number;\n  'COD (mm)': number;\n}\n\n// Available beta angles (for future use)\n// const BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];\n\nconst initialTableData: TableData[] = [\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 370, 'KIC (MPa√m)': 0.16 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 190, 'KIC (MPa√m)': 0.10 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 195, 'KIC (MPa√m)': 0.10 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 250, 'KIC (MPa√m)': 0.11 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 510, 'KIC (MPa√m)': 0.22 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 320, 'KIC (MPa√m)': 0.14 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 296, 'KIC (MPa√m)': 0.13 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 452, 'KIC (MPa√m)': 0.19 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 384, 'KIC (MPa√m)': 0.16 },\n  { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 348, 'KIC (MPa√m)': 0.14 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 340, 'KIC (MPa√m)': 0.15 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 720, 'KIC (MPa√m)': 0.31 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 630, 'KIC (MPa√m)': 0.27 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 486, 'KIC (MPa√m)': 0.21 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 398, 'KIC (MPa√m)': 0.17 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 615, 'KIC (MPa√m)': 0.26 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 532, 'KIC (MPa√m)': 0.23 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 478, 'KIC (MPa√m)': 0.21 },\n  { 'SCB Numuneleri': 'β=30°', 'Pmax (N)': 548, 'KIC (MPa√m)': 0.23 },\n];\n\nconst initialCODData: CODData[] = [\n  { 'Yük (kN)': 0.00, 'COD (mm)': 0.00 },\n  { 'Yük (kN)': 0.05, 'COD (mm)': 0.06 },\n  { 'Yük (kN)': 0.10, 'COD (mm)': 0.12 },\n  { 'Yük (kN)': 0.15, 'COD (mm)': 0.16 },\n  { 'Yük (kN)': 0.20, 'COD (mm)': 0.18 },\n  { 'Yük (kN)': 0.25, 'COD (mm)': 0.195 },\n  { 'Yük (kN)': 0.30, 'COD (mm)': 0.20 },\n  { 'Yük (kN)': 0.35, 'COD (mm)': 0.195 },\n  { 'Yük (kN)': 0.40, 'COD (mm)': 0.18 },\n  { 'Yük (kN)': 0.45, 'COD (mm)': 0.16 },\n  { 'Yük (kN)': 0.50, 'COD (mm)': 0.13 },\n  { 'Yük (kN)': 0.55, 'COD (mm)': 0.10 },\n  { 'Yük (kN)': 0.60, 'COD (mm)': 0.07 },\n  { 'Yük (kN)': 0.65, 'COD (mm)': 0.05 },\n  { 'Yük (kN)': 0.70, 'COD (mm)': 0.03 },\n  { 'Yük (kN)': 0.75, 'COD (mm)': 0.01 },\n  { 'Yük (kN)': 0.80, 'COD (mm)': 0.00 },\n];\n\nexport default function DataAnalysis() {\n  const [tableData, setTableData] = useState<TableData[]>(initialTableData);\n  const [codData, setCodData] = useState<CODData[]>(initialCODData);\n  const [showKICChart, setShowKICChart] = useState(false);\n  const [showCODChart, setShowCODChart] = useState(false);\n  const [showSCBUploadModal, setShowSCBUploadModal] = useState(false);\n  const [showCODUploadModal, setShowCODUploadModal] = useState(false);\n\n  const addTableRow = () => {\n    setTableData([...tableData, { 'SCB Numuneleri': 'β=0°', 'Pmax (N)': 0, 'KIC (MPa√m)': 0 }]);\n  };\n\n  // Calculate averages for each beta angle dynamically when needed\n  // const calculateAverages = () => {\n  //   const averages: Record<string, { avgPmax: number; avgKIC: number; count: number }> = {};\n  //   BETA_ANGLES.forEach(angle => {\n  //     const angleData = tableData.filter(row => row['SCB Numuneleri'] === angle);\n  //     if (angleData.length > 0) {\n  //       const avgPmax = angleData.reduce((sum, row) => sum + row['Pmax (N)'], 0) / angleData.length;\n  //       const avgKIC = angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length;\n  //       averages[angle] = { avgPmax, avgKIC, count: angleData.length };\n  //     }\n  //   });\n  //   return averages;\n  // };\n\n  const removeTableRow = (index: number) => {\n    setTableData(tableData.filter((_, i) => i !== index));\n  };\n\n  const updateTableData = (index: number, field: keyof TableData, value: string | number) => {\n    const newData = [...tableData];\n    newData[index] = { ...newData[index], [field]: value };\n    setTableData(newData);\n  };\n\n  const addCODRow = () => {\n    setCodData([...codData, { 'Yük (kN)': 0, 'COD (mm)': 0 }]);\n  };\n\n  const removeCODRow = (index: number) => {\n    setCodData(codData.filter((_, i) => i !== index));\n  };\n\n  const updateCODData = (index: number, field: keyof CODData, value: number) => {\n    const newData = [...codData];\n    newData[index] = { ...newData[index], [field]: value };\n    setCodData(newData);\n  };\n\n  const exportCSV = (data: TableData[] | CODData[], filename: string) => {\n    const csv = [\n      Object.keys(data[0]).join(','),\n      ...data.map(row => Object.values(row).join(','))\n    ].join('\\n');\n\n    const blob = new Blob([csv], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = filename;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  const handleSCBCSVUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const csv = e.target?.result as string;\n        const lines = csv.split('\\n').filter(line => line.trim());\n        const headers = lines[0].split(',').map(h => h.trim());\n\n        // Validate headers\n        const expectedHeaders = ['SCB Numuneleri', 'Pmax (N)', 'KIC (MPa√m)'];\n        const isValidFormat = expectedHeaders.every(header => headers.includes(header));\n\n        if (!isValidFormat) {\n          alert('CSV formatı hatalı! Başlıklar: SCB Numuneleri, Pmax (N), KIC (MPa√m) olmalıdır.');\n          return;\n        }\n\n        const newData: TableData[] = [];\n        for (let i = 1; i < lines.length; i++) {\n          const values = lines[i].split(',').map(v => v.trim());\n          if (values.length >= 3) {\n            newData.push({\n              'SCB Numuneleri': values[0],\n              'Pmax (N)': parseFloat(values[1]) || 0,\n              'KIC (MPa√m)': parseFloat(values[2]) || 0,\n            });\n          }\n        }\n\n        setTableData(newData);\n        setShowSCBUploadModal(false);\n        alert(`${newData.length} satır başarıyla yüklendi!`);\n      } catch {\n        alert('CSV dosyası okunurken hata oluştu!');\n      }\n    };\n    reader.readAsText(file);\n    event.target.value = ''; // Reset input\n  };\n\n  const handleCODCSVUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const csv = e.target?.result as string;\n        const lines = csv.split('\\n').filter(line => line.trim());\n        const headers = lines[0].split(',').map(h => h.trim());\n\n        // Validate headers\n        const expectedHeaders = ['Yük (kN)', 'COD (mm)'];\n        const isValidFormat = expectedHeaders.every(header => headers.includes(header));\n\n        if (!isValidFormat) {\n          alert('CSV formatı hatalı! Başlıklar: Yük (kN), COD (mm) olmalıdır.');\n          return;\n        }\n\n        const newData: CODData[] = [];\n        for (let i = 1; i < lines.length; i++) {\n          const values = lines[i].split(',').map(v => v.trim());\n          if (values.length >= 2) {\n            newData.push({\n              'Yük (kN)': parseFloat(values[0]) || 0,\n              'COD (mm)': parseFloat(values[1]) || 0,\n            });\n          }\n        }\n\n        setCodData(newData);\n        setShowCODUploadModal(false);\n        alert(`${newData.length} satır başarıyla yüklendi!`);\n      } catch {\n        alert('CSV dosyası okunurken hata oluştu!');\n      }\n    };\n    reader.readAsText(file);\n    event.target.value = ''; // Reset input\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* SCB Test Results */}\n      <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-slate-800 flex items-center\">\n            <div className=\"p-3 bg-blue-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\">\n              <BarChart3 className=\"h-6 w-6 text-white\" />\n            </div>\n            SCB Kırılma Tokluğu Test Sonuçları\n          </h2>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={addTableRow}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              <span>Satır Ekle</span>\n            </button>\n            <button\n              onClick={() => setShowSCBUploadModal(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n            >\n              <Upload className=\"h-4 w-4\" />\n              <span>CSV Yükle</span>\n            </button>\n            <button\n              onClick={() => exportCSV(tableData, 'scb-test-results.csv')}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>CSV İndir</span>\n            </button>\n          </div>\n        </div>\n\n        <DataTable\n          data={tableData}\n          onUpdate={updateTableData}\n          onRemove={removeTableRow}\n        />\n\n        <div className=\"mt-4 flex space-x-2\">\n          <button\n            onClick={() => setShowKICChart(!showKICChart)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium\"\n          >\n            {showKICChart ? 'Grafiği Gizle' : 'KIC Grafiğini Oluştur'}\n          </button>\n        </div>\n\n        {showKICChart && (\n          <div className=\"mt-6\">\n            <h3 className=\"text-lg font-bold text-gray-900 mb-4\">KIC Değerleri Grafiği (Pmax vs KIC)</h3>\n            <ScatterChart data={tableData} />\n          </div>\n        )}\n      </div>\n\n      {/* Load-COD Curve */}\n      <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-slate-800 flex items-center\">\n            <div className=\"p-3 bg-green-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\">\n              <BarChart3 className=\"h-6 w-6 text-white\" />\n            </div>\n            Yük-COD Eğrisi Görselleştirme\n          </h2>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={addCODRow}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              <span>Satır Ekle</span>\n            </button>\n            <button\n              onClick={() => setShowCODUploadModal(true)}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm\"\n            >\n              <Upload className=\"h-4 w-4\" />\n              <span>CSV Yükle</span>\n            </button>\n            <button\n              onClick={() => exportCSV(codData, 'load-cod-data.csv')}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>CSV İndir</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                  Yük (kN)\n                </th>\n                <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                  COD (mm)\n                </th>\n                <th className=\"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider\">\n                  İşlemler\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {codData.map((row, index) => (\n                <tr key={index}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      value={row['Yük (kN)']}\n                      onChange={(e) => updateCODData(index, 'Yük (kN)', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium\"\n                    />\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <input\n                      type=\"number\"\n                      step=\"0.001\"\n                      value={row['COD (mm)']}\n                      onChange={(e) => updateCODData(index, 'COD (mm)', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium\"\n                    />\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <button\n                      onClick={() => removeCODRow(index)}\n                      className=\"text-red-600 hover:text-red-900\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        <div className=\"mt-4\">\n          <button\n            onClick={() => setShowCODChart(!showCODChart)}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium\"\n          >\n            {showCODChart ? 'Grafiği Gizle' : 'Yük-COD Eğrisini Oluştur'}\n          </button>\n        </div>\n\n        {showCODChart && (\n          <div className=\"mt-6\">\n            <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Tipik Yük-COD Eğrisi</h3>\n            <LineChart data={codData} />\n          </div>\n        )}\n      </div>\n\n      {/* SCB CSV Upload Modal */}\n      {showSCBUploadModal && (\n        <div\n          className=\"fixed inset-0 flex items-center justify-center z-50\"\n          onClick={() => setShowSCBUploadModal(false)}\n        >\n          {/* Blur background overlay */}\n          <div\n            className=\"absolute inset-0 bg-gray-200 bg-opacity-60\"\n            style={{\n              backdropFilter: 'blur(4px)',\n              WebkitBackdropFilter: 'blur(4px)'\n            }}\n          ></div>\n          {/* Modal content */}\n          <div\n            className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-bold text-gray-900 flex items-center\">\n                <Info className=\"h-5 w-5 mr-2 text-blue-600\" />\n                SCB CSV Dosyası Yükleme\n              </h3>\n              <button\n                onClick={() => setShowSCBUploadModal(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"mb-4\">\n              <p className=\"text-base font-semibold text-gray-900 mb-3\">\n                CSV dosyanız aşağıdaki formatta olmalıdır:\n              </p>\n              <div className=\"bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono\">\n                <div className=\"font-bold text-gray-900 mb-1\">SCB Numuneleri,Pmax (N),KIC (MPa√m)</div>\n                <div className=\"text-gray-800\">β=0°,370,0.16</div>\n                <div className=\"text-gray-800\">β=30°,720,0.31</div>\n                <div className=\"text-gray-800\">β=45°,450,0.20</div>\n                <div className=\"text-gray-600\">...</div>\n              </div>\n              <div className=\"text-sm font-medium text-gray-800 mt-3 space-y-1\">\n                <div>• İlk satır başlık satırı olmalıdır</div>\n                <div>• Beta açıları: β=0°, β=15°, β=30°, β=45°, β=60°, β=75°, β=90°</div>\n                <div>• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır</div>\n              </div>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <label className=\"flex-1\">\n                <input\n                  type=\"file\"\n                  accept=\".csv\"\n                  onChange={handleSCBCSVUpload}\n                  className=\"hidden\"\n                />\n                <div className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center\">\n                  Dosya Seç ve Yükle\n                </div>\n              </label>\n              <button\n                onClick={() => setShowSCBUploadModal(false)}\n                className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n              >\n                İptal\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* COD CSV Upload Modal */}\n      {showCODUploadModal && (\n        <div\n          className=\"fixed inset-0 flex items-center justify-center z-50\"\n          onClick={() => setShowCODUploadModal(false)}\n        >\n          {/* Blur background overlay */}\n          <div\n            className=\"absolute inset-0 bg-gray-200 bg-opacity-60\"\n            style={{\n              backdropFilter: 'blur(4px)',\n              WebkitBackdropFilter: 'blur(4px)'\n            }}\n          ></div>\n          {/* Modal content */}\n          <div\n            className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-bold text-gray-900 flex items-center\">\n                <Info className=\"h-5 w-5 mr-2 text-blue-600\" />\n                COD CSV Dosyası Yükleme\n              </h3>\n              <button\n                onClick={() => setShowCODUploadModal(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"mb-4\">\n              <p className=\"text-base font-semibold text-gray-900 mb-3\">\n                CSV dosyanız aşağıdaki formatta olmalıdır:\n              </p>\n              <div className=\"bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono\">\n                <div className=\"font-bold text-gray-900 mb-1\">Yük (kN),COD (mm)</div>\n                <div className=\"text-gray-800\">0.00,0.00</div>\n                <div className=\"text-gray-800\">0.05,0.06</div>\n                <div className=\"text-gray-800\">0.10,0.12</div>\n                <div className=\"text-gray-600\">...</div>\n              </div>\n              <div className=\"text-sm font-medium text-gray-800 mt-3 space-y-1\">\n                <div>• İlk satır başlık satırı olmalıdır</div>\n                <div>• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır</div>\n                <div>• Yük değerleri kN cinsinden, COD değerleri mm cinsinden</div>\n              </div>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <label className=\"flex-1\">\n                <input\n                  type=\"file\"\n                  accept=\".csv\"\n                  onChange={handleCODCSVUpload}\n                  className=\"hidden\"\n                />\n                <div className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center\">\n                  Dosya Seç ve Yükle\n                </div>\n              </label>\n              <button\n                onClick={() => setShowCODUploadModal(false)}\n                className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n              >\n                İptal\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAmBA,yCAAyC;AACzC,sFAAsF;AAEtF,MAAM,mBAAgC;IACpC;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAQ,YAAY;QAAK,eAAe;IAAK;IACjE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;IAClE;QAAE,kBAAkB;QAAS,YAAY;QAAK,eAAe;IAAK;CACnE;AAED,MAAM,iBAA4B;IAChC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAM;IACtC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAM;IACtC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;IACrC;QAAE,YAAY;QAAM,YAAY;IAAK;CACtC;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,cAAc;QAClB,aAAa;eAAI;YAAW;gBAAE,kBAAkB;gBAAQ,YAAY;gBAAG,eAAe;YAAE;SAAE;IAC5F;IAEA,iEAAiE;IACjE,oCAAoC;IACpC,6FAA6F;IAC7F,mCAAmC;IACnC,kFAAkF;IAClF,kCAAkC;IAClC,qGAAqG;IACrG,uGAAuG;IACvG,wEAAwE;IACxE,QAAQ;IACR,QAAQ;IACR,qBAAqB;IACrB,KAAK;IAEL,MAAM,iBAAiB,CAAC;QACtB,aAAa,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAChD;IAEA,MAAM,kBAAkB,CAAC,OAAe,OAAwB;QAC9D,MAAM,UAAU;eAAI;SAAU;QAC9B,OAAO,CAAC,MAAM,GAAG;YAAE,GAAG,OAAO,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QACrD,aAAa;IACf;IAEA,MAAM,YAAY;QAChB,WAAW;eAAI;YAAS;gBAAE,YAAY;gBAAG,YAAY;YAAE;SAAE;IAC3D;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC5C;IAEA,MAAM,gBAAgB,CAAC,OAAe,OAAsB;QAC1D,MAAM,UAAU;eAAI;SAAQ;QAC5B,OAAO,CAAC,MAAM,GAAG;YAAE,GAAG,OAAO,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QACrD,WAAW;IACb;IAEA,MAAM,YAAY,CAAC,MAA+B;QAChD,MAAM,MAAM;YACV,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;eACvB,KAAK,GAAG,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC;SAC5C,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAI,EAAE;YAAE,MAAM;QAAW;QAChD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,MAAM,EAAE,MAAM,EAAE;gBACtB,MAAM,QAAQ,IAAI,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;gBACtD,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAEnD,mBAAmB;gBACnB,MAAM,kBAAkB;oBAAC;oBAAkB;oBAAY;iBAAc;gBACrE,MAAM,gBAAgB,gBAAgB,KAAK,CAAC,CAAA,SAAU,QAAQ,QAAQ,CAAC;gBAEvE,IAAI,CAAC,eAAe;oBAClB,MAAM;oBACN;gBACF;gBAEA,MAAM,UAAuB,EAAE;gBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBAClD,IAAI,OAAO,MAAM,IAAI,GAAG;wBACtB,QAAQ,IAAI,CAAC;4BACX,kBAAkB,MAAM,CAAC,EAAE;4BAC3B,YAAY,WAAW,MAAM,CAAC,EAAE,KAAK;4BACrC,eAAe,WAAW,MAAM,CAAC,EAAE,KAAK;wBAC1C;oBACF;gBACF;gBAEA,aAAa;gBACb,sBAAsB;gBACtB,MAAM,GAAG,QAAQ,MAAM,CAAC,0BAA0B,CAAC;YACrD,EAAE,OAAM;gBACN,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;QAClB,MAAM,MAAM,CAAC,KAAK,GAAG,IAAI,cAAc;IACzC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,MAAM,EAAE,MAAM,EAAE;gBACtB,MAAM,QAAQ,IAAI,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;gBACtD,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAEnD,mBAAmB;gBACnB,MAAM,kBAAkB;oBAAC;oBAAY;iBAAW;gBAChD,MAAM,gBAAgB,gBAAgB,KAAK,CAAC,CAAA,SAAU,QAAQ,QAAQ,CAAC;gBAEvE,IAAI,CAAC,eAAe;oBAClB,MAAM;oBACN;gBACF;gBAEA,MAAM,UAAqB,EAAE;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBAClD,IAAI,OAAO,MAAM,IAAI,GAAG;wBACtB,QAAQ,IAAI,CAAC;4BACX,YAAY,WAAW,MAAM,CAAC,EAAE,KAAK;4BACrC,YAAY,WAAW,MAAM,CAAC,EAAE,KAAK;wBACvC;oBACF;gBACF;gBAEA,WAAW;gBACX,sBAAsB;gBACtB,MAAM,GAAG,QAAQ,MAAM,CAAC,0BAA0B,CAAC;YACrD,EAAE,OAAM;gBACN,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;QAClB,MAAM,MAAM,CAAC,KAAK,GAAG,IAAI,cAAc;IACzC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCACjB;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,UAAU,WAAW;wCACpC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,8OAAC,+HAAA,CAAA,UAAS;wBACR,MAAM;wBACN,UAAU;wBACV,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAU;sCAET,eAAe,kBAAkB;;;;;;;;;;;oBAIrC,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC,kIAAA,CAAA,UAAY;gCAAC,MAAM;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCACjB;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,UAAU,SAAS;wCAClC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8OAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8OAAC;gDAAG,WAAU;0DAA+E;;;;;;;;;;;;;;;;;8CAKjG,8OAAC;oCAAM,WAAU;8CACd,QAAQ,GAAG,CAAC,CAAC,KAAK,sBACjB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,GAAG,CAAC,WAAW;wDACtB,UAAU,CAAC,IAAM,cAAc,OAAO,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAChF,WAAU;;;;;;;;;;;8DAGd,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,GAAG,CAAC,WAAW;wDACtB,UAAU,CAAC,IAAM,cAAc,OAAO,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAChF,WAAU;;;;;;;;;;;8DAGd,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDACC,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CAxBf;;;;;;;;;;;;;;;;;;;;;kCAiCjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAU;sCAET,eAAe,kBAAkB;;;;;;;;;;;oBAIrC,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC,+HAAA,CAAA,UAAS;gCAAC,MAAM;;;;;;;;;;;;;;;;;;YAMtB,oCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,sBAAsB;;kCAGrC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,gBAAgB;4BAChB,sBAAsB;wBACxB;;;;;;kCAGF,8OAAC;wBACC,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGjD,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;;;;;;;;;;;;;0CAIT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DAAkG;;;;;;;;;;;;kDAInH,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YASR,oCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,sBAAsB;;kCAGrC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,gBAAgB;4BAChB,sBAAsB;wBACxB;;;;;;kCAGF,8OAAC;wBACC,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGjD,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;;;;;;;;;;;;;0CAIT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DAAkG;;;;;;;;;;;;kDAInH,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { BarChart3, MessageCircle, LogOut } from 'lucide-react';\nimport Image from 'next/image';\nimport ChatAssistant from '@/components/ChatAssistant';\nimport DataAnalysis from '@/components/DataAnalysis';\n\ninterface Message {\n  role: 'user' | 'assistant';\n  content: string;\n}\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'chat' | 'analysis'>('chat');\n  const [chatMessages, setChatMessages] = useState<Message[]>([]);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n      router.push('/login');\n      router.refresh();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-100\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 md:h-20\">\n            <div className=\"flex items-center space-x-2 md:space-x-4 min-w-0 flex-1\">\n              <div className=\"p-1.5 md:p-2 bg-white rounded-lg md:rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex-shrink-0\">\n                <Image\n                  src=\"/demokrasilogo.png\"\n                  alt=\"Demokrasi Üniversitesi Logo\"\n                  width={32}\n                  height={32}\n                  className=\"object-contain md:w-12 md:h-12\"\n                />\n              </div>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-lg md:text-2xl font-bold text-slate-800 truncate\">\n                  Kırılma Tokluğu Asistanı\n                </h1>\n                <p className=\"text-xs md:text-sm text-slate-600 truncate\">\n                  Yapay Zeka Destekli Hesaplama ve Analiz\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation Tabs and Logout in Header */}\n            <div className=\"flex items-center space-x-1 md:space-x-3 flex-shrink-0\">\n              <button\n                onClick={() => setActiveTab('chat')}\n                className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ${\n                  activeTab === 'chat'\n                    ? 'bg-blue-600 text-white shadow-md hover:scale-105'\n                    : 'text-slate-700 hover:text-slate-900 hover:bg-gray-50'\n                }`}\n              >\n                <MessageCircle className=\"h-3 w-3 md:h-4 md:w-4\" />\n                <span className=\"hidden sm:inline\">Kırılma Tokluğu Asistanı</span>\n                <span className=\"sm:hidden\">Chat</span>\n              </button>\n              <button\n                onClick={() => setActiveTab('analysis')}\n                className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ${\n                  activeTab === 'analysis'\n                    ? 'bg-blue-600 text-white shadow-md hover:scale-105'\n                    : 'text-slate-700 hover:text-slate-900 hover:bg-gray-50'\n                }`}\n              >\n                <BarChart3 className=\"h-3 w-3 md:h-4 md:w-4\" />\n                <span className=\"hidden sm:inline\">Laboratuvar Veri Analizi</span>\n                <span className=\"sm:hidden\">Analiz</span>\n              </button>\n\n              {/* Logout Button */}\n              <button\n                onClick={handleLogout}\n                className=\"flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200 hover:scale-105\"\n                title=\"Çıkış Yap\"\n              >\n                <LogOut className=\"h-3 w-3 md:h-4 md:w-4\" />\n                <span className=\"hidden lg:inline\">Çıkış</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'chat' && (\n          <ChatAssistant\n            messages={chatMessages}\n            setMessages={setChatMessages}\n          />\n        )}\n        {activeTab === 'analysis' && <DataAnalysis />}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC9D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;YACV;YACA,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yJAAyJ,EACnK,cAAc,SACV,qDACA,wDACJ;;0DAEF,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAE9B,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yJAAyJ,EACnK,cAAc,aACV,qDACA,wDACJ;;0DAEF,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAI9B,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;;0DAEN,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC;gBAAK,WAAU;;oBACb,cAAc,wBACb,8OAAC,mIAAA,CAAA,UAAa;wBACZ,UAAU;wBACV,aAAa;;;;;;oBAGhB,cAAc,4BAAc,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;AAIlD", "debugId": null}}]}