'use client';

import { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ScatterController,
  TooltipItem,
} from 'chart.js';
import { Scatter } from 'react-chartjs-2';
import { Download } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ScatterController
);

interface TableData {
  'SCB Numuneleri': string;
  'Pmax (N)': number;
  'KIC (MPa√m)': number;
}

interface ScatterChartProps {
  data: TableData[];
}

// Available beta angles and their colors
const BETA_ANGLES = ['β=0°', 'β=15°', 'β=30°', 'β=45°', 'β=60°', 'β=75°', 'β=90°'];
const BETA_COLORS = [
  { bg: 'rgba(59, 130, 246, 0.7)', border: 'rgb(59, 130, 246)' }, // Blue
  { bg: 'rgba(34, 197, 94, 0.7)', border: 'rgb(34, 197, 94)' },   // Green
  { bg: 'rgba(239, 68, 68, 0.7)', border: 'rgb(239, 68, 68)' },   // Red
  { bg: 'rgba(245, 158, 11, 0.7)', border: 'rgb(245, 158, 11)' }, // Yellow
  { bg: 'rgba(168, 85, 247, 0.7)', border: 'rgb(168, 85, 247)' }, // Purple
  { bg: 'rgba(236, 72, 153, 0.7)', border: 'rgb(236, 72, 153)' }, // Pink
  { bg: 'rgba(99, 102, 241, 0.7)', border: 'rgb(99, 102, 241)' }, // Indigo
];

export default function ScatterChart({ data }: ScatterChartProps) {
  const chartRef = useRef<ChartJS<'scatter'>>(null);

  const downloadChart = () => {
    const chart = chartRef.current;
    if (!chart) return;

    const canvas = chart.canvas;

    // Create a new canvas with white background
    const newCanvas = document.createElement('canvas');
    const newCtx = newCanvas.getContext('2d');
    newCanvas.width = canvas.width;
    newCanvas.height = canvas.height;

    // Fill with white background
    if (newCtx) {
      newCtx.fillStyle = '#ffffff';
      newCtx.fillRect(0, 0, newCanvas.width, newCanvas.height);

      // Draw the original chart on top
      newCtx.drawImage(canvas, 0, 0);
    }

    const url = newCanvas.toDataURL('image/jpeg', 0.95);

    const link = document.createElement('a');
    link.download = 'kic-scatter-chart.jpg';
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Group data by beta angles and calculate averages
  const groupedData = BETA_ANGLES.map((angle, index) => {
    const angleData = data.filter(row => row['SCB Numuneleri'] === angle);
    const average = angleData.length > 0
      ? angleData.reduce((sum, row) => sum + row['KIC (MPa√m)'], 0) / angleData.length
      : 0;

    return {
      angle,
      data: angleData,
      average,
      color: BETA_COLORS[index] || BETA_COLORS[0],
      count: angleData.length
    };
  }).filter(group => group.count > 0); // Only include groups with data

  const chartData = {
    datasets: groupedData.map((group, index) => ({
      label: `${group.angle} (${group.count} numune)`,
      data: group.data.map(row => ({
        x: row['Pmax (N)'],
        y: row['KIC (MPa√m)']
      })),
      backgroundColor: group.color.bg,
      borderColor: group.color.border,
      pointRadius: 6,
      pointHoverRadius: 8,
      pointStyle: index % 2 === 0 ? 'circle' : 'cross', // Alternate between circle and cross
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'SCB Kırılma Tokluğu Test Sonuçları',
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: TooltipItem<'scatter'>) {
            const label = context.dataset.label || '';
            return `${label}: Pmax=${context.parsed.x}N, KIC=${context.parsed.y}MPa√m`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Pmax (N)',
          font: {
            size: 14,
            weight: 'bold' as const,
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'KIC (MPa√m)',
          font: {
            size: 14,
            weight: 'bold' as const,
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'point' as const,
    },
  };

  // Add average lines after chart is rendered
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart) return;

    const ctx = chart.ctx;
    const chartArea = chart.chartArea;

    if (!chartArea) return;

    // Draw average lines for each group
    ctx.save();

    groupedData.forEach((group, index) => {
      if (group.average > 0) {
        ctx.strokeStyle = group.color.border.replace('rgb', 'rgba').replace(')', ', 0.8)');
        ctx.setLineDash([5, 5]);
        ctx.lineWidth = 2;
        ctx.beginPath();
        const y = chart.scales.y.getPixelForValue(group.average);
        ctx.moveTo(chartArea.left, y);
        ctx.lineTo(chartArea.right, y);
        ctx.stroke();

        // Add text label
        ctx.fillStyle = group.color.border;
        ctx.font = '12px Arial';
        const labelY = y - 5 - (index * 15); // Offset labels to avoid overlap
        ctx.fillText(`${group.angle} Ort: ${group.average.toFixed(2)}`, chartArea.right - 120, labelY);
      }
    });

    ctx.restore();
  }, [groupedData]);

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <button
          onClick={downloadChart}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
        >
          <Download className="h-4 w-4" />
          <span>Grafiği İndir</span>
        </button>
      </div>
      <div className="h-96 w-full">
        <Scatter ref={chartRef} data={chartData} options={options} />
      </div>
    </div>
  );
}
