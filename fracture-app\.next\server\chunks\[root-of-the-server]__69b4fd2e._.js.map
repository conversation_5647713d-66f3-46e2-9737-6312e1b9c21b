{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/fracture-main/fracture-main/fracture-app/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY,\n});\n\nconst MODEL_NAME = \"claude-3-5-sonnet-20241022\";\n\nconst SYSTEM_PROMPT = `\nYou are an AI assistant specialized in materials science and mechanics.\nYour main task is to help the user with the \"Fracture Toughness (K_IC)\" formula.\n\nHere are the basic formula and variables you need to know:\n\nFORMULA:\nK_IC = (P_max / (B * sqrt(D))) * Y_star_min\n\nVARIABLES:\n- K_IC: Fracture Toughness (Unit: MPa)\n- P_max: Maximum Fracture Load (Unit: MN - Meganewton)\n- B: Sample Thickness (Unit: m - Meter)\n- D: Sample Diameter (Unit: m - Meter)\n- Y_star_min: Critical Dimensionless Shape Factor (Unit: Dimensionless)\n\nINTERACTION RULES:\n1. When the user wants to calculate, ask for the necessary P_max, B, D, and Y_star_min values.\n2. IMPORTANT: Always check if the user has provided units for each value. If units are missing, ask the user to specify the correct units:\n   - P_max should be in MN (<PERSON>ewton) or specify the unit\n   - B should be in m (Meter) or specify the unit\n   - D should be in m (Meter) or specify the unit\n   - Y_star_min is dimensionless (no unit needed)\n3. If the user provides all values WITH proper units, calculate the K_IC value step-by-step using the formula.\n4. Clearly show the calculation steps (formula, substitution with units, and result).\n5. Briefly explain what the result means.\n6. Answer theoretical questions about the formula or variables (e.g., \"What is fracture toughness?\") based on your expert knowledge.\n7. Always use a polite, professional, and helpful language. Always speak Turkish Language.\n`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { messages } = await request.json();\n\n    if (!process.env.ANTHROPIC_API_KEY) {\n      return NextResponse.json(\n        { error: 'Anthropic API key not configured' },\n        { status: 500 }\n      );\n    }\n\n    const stream = await anthropic.messages.create({\n      model: MODEL_NAME,\n      system: SYSTEM_PROMPT,\n      messages: messages,\n      max_tokens: 2048,\n      stream: true,\n    });\n\n    const encoder = new TextEncoder();\n    const readable = new ReadableStream({\n      async start(controller) {\n        try {\n          for await (const chunk of stream) {\n            if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {\n              const text = chunk.delta.text;\n              controller.enqueue(encoder.encode(`data: ${JSON.stringify({ text })}\\n\\n`));\n            }\n          }\n          controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n          controller.close();\n        } catch (error) {\n          controller.error(error);\n        }\n      },\n    });\n\n    return new Response(readable, {\n      headers: {\n        'Content-Type': 'text/event-stream',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n      },\n    });\n  } catch (error) {\n    console.error('Chat API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;AACvC;AAEA,MAAM,aAAa;AAEnB,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BvB,CAAC;AAEM,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;YAC7C,OAAO;YACP,QAAQ;YACR,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;QAEA,MAAM,UAAU,IAAI;QACpB,MAAM,WAAW,IAAI,eAAe;YAClC,MAAM,OAAM,UAAU;gBACpB,IAAI;oBACF,WAAW,MAAM,SAAS,OAAQ;wBAChC,IAAI,MAAM,IAAI,KAAK,yBAAyB,MAAM,KAAK,CAAC,IAAI,KAAK,cAAc;4BAC7E,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI;4BAC7B,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;gCAAE;4BAAK,GAAG,IAAI,CAAC;wBAC3E;oBACF;oBACA,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBAClC,WAAW,KAAK;gBAClB,EAAE,OAAO,OAAO;oBACd,WAAW,KAAK,CAAC;gBACnB;YACF;QACF;QAEA,OAAO,IAAI,SAAS,UAAU;YAC5B,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;YAChB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}