(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2921:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>I});var s=a(5155),l=a(2115),r=a(5695),i=a(1366),n=a(2713),d=a(4835),o=a(6766),c=a(5657),m=a(1007),x=a(5196),h=a(4357),u=a(1154),p=a(2486);function g(e){let{messages:t,setMessages:a}=e,[r,i]=(0,l.useState)(""),[n,d]=(0,l.useState)(!1),[o,g]=(0,l.useState)(null),b=(0,l.useRef)(null),N=()=>{var e;null==(e=b.current)||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{N()},[t]);let f=e=>{let t="";switch(e){case"calculation":t="Kırılma tokluğu hesaplaması yapmak istiyorum. Hangi değerleri girmeliyim?";break;case"theory":t="Kırılma tokluğu nedir? Teorik bilgi verebilir misin?";break;case"variables":t="Pmax, B, D ve Y* değişkenlerinin ne anlama geldiğini a\xe7ıklar mısın?"}i(t),setTimeout(()=>{y()},100)},y=async()=>{if(!r.trim()||n)return;let e=[...t,{role:"user",content:r}];a(e),i(""),d(!0);try{var s;let t=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:e})});if(!t.ok)throw Error("Failed to send message");let l=null==(s=t.body)?void 0:s.getReader();if(!l)throw Error("No reader available");let r="";for(a([...e,{role:"assistant",content:""}]);;){let{done:t,value:s}=await l.read();if(t)break;for(let t of new TextDecoder().decode(s).split("\n"))if(t.startsWith("data: ")){let s=t.slice(6);if("[DONE]"===s)break;try{let t=JSON.parse(s);t.text&&(r+=t.text,a([...e,{role:"assistant",content:r}]))}catch(e){}}}}catch(t){console.error("Error sending message:",t),a([...e,{role:"assistant",content:"\xdczg\xfcn\xfcm, bir hata oluştu. L\xfctfen tekrar deneyin."}])}finally{d(!1)}},j=async(e,t)=>{try{await navigator.clipboard.writeText(e),g(t),setTimeout(()=>g(null),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 h-[calc(100vh-200px)] min-h-[500px] max-h-[800px] flex flex-col",children:[(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 md:p-6 space-y-4",children:[0===t.length&&(0,s.jsxs)("div",{className:"text-center text-slate-700 mt-8",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-600 rounded-2xl w-16 h-16 mx-auto mb-6 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("p",{className:"text-2xl font-bold text-slate-800",children:"Kırılma Tokluğu Asistanına Hoş Geldiniz!"}),(0,s.jsx)("p",{className:"text-base mt-3 mb-8 font-medium text-slate-600",children:"Hesaplama yapmak i\xe7in değerleri girin veya teorik sorularınızı sorun."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 max-w-2xl mx-auto px-2 md:px-0",children:[(0,s.jsxs)("button",{onClick:()=>f("calculation"),className:"flex flex-col items-center p-3 md:p-4 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 hover:border-green-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md",children:[(0,s.jsx)("div",{className:"p-2 bg-green-600 rounded-lg mb-2",children:(0,s.jsx)("span",{className:"text-white text-lg",children:"✅"})}),(0,s.jsx)("span",{className:"font-semibold text-slate-800 text-sm md:text-base",children:"Hesaplama Yap"}),(0,s.jsx)("span",{className:"text-xs md:text-sm text-slate-600 mt-1",children:"KIC değeri hesapla"})]}),(0,s.jsxs)("button",{onClick:()=>f("theory"),className:"flex flex-col items-center p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-600 rounded-lg mb-2",children:(0,s.jsx)("span",{className:"text-white text-lg",children:"\uD83D\uDCD8"})}),(0,s.jsx)("span",{className:"font-semibold text-slate-800 text-sm md:text-base",children:"Teorik Bilgi"}),(0,s.jsx)("span",{className:"text-xs md:text-sm text-slate-600 mt-1",children:"Kırılma tokluğu nedir?"})]}),(0,s.jsxs)("button",{onClick:()=>f("variables"),className:"flex flex-col items-center p-3 md:p-4 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-600 rounded-lg mb-2",children:(0,s.jsx)("span",{className:"text-white text-lg",children:"ℹ️"})}),(0,s.jsx)("span",{className:"font-semibold text-slate-800 text-sm md:text-base",children:"Değişken A\xe7ıklamaları"}),(0,s.jsx)("span",{className:"text-xs md:text-sm text-slate-600 mt-1",children:"Pmax, B, D, Y* nedir?"})]})]})]}),t.map((e,t)=>(0,s.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"," group"),children:(0,s.jsxs)("div",{className:"max-w-[85%] md:max-w-[80%] rounded-xl px-3 md:px-4 py-2 md:py-3 relative shadow-sm ".concat("user"===e.role?"bg-blue-600 text-white":"bg-gray-100 text-slate-800"),children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:["assistant"===e.role&&(0,s.jsx)(c.A,{className:"h-4 w-4 mt-0.5 text-blue-600"}),"user"===e.role&&(0,s.jsx)(m.A,{className:"h-4 w-4 mt-0.5 text-white"}),(0,s.jsx)("div",{className:"whitespace-pre-wrap flex-1",children:e.content})]}),(0,s.jsx)("button",{onClick:()=>j(e.content,t),className:"absolute top-2 right-2 p-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-105 ".concat("user"===e.role?"hover:bg-blue-700 text-white":"hover:bg-gray-200 text-slate-600"),title:"Metni kopyala",children:o===t?(0,s.jsx)(x.A,{className:"h-3 w-3"}):(0,s.jsx)(h.A,{className:"h-3 w-3"})})]})},t)),n&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsx)("div",{className:"bg-gray-100 rounded-xl px-4 py-3 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-1 bg-blue-600 rounded-lg",children:(0,s.jsx)(c.A,{className:"h-3 w-3 text-white"})}),(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin text-blue-600"}),(0,s.jsx)("span",{className:"text-slate-600",children:"Yazıyor..."})]})})}),(0,s.jsx)("div",{ref:b})]}),(0,s.jsx)("div",{className:"p-4 md:p-6 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"flex space-x-2 md:space-x-3",children:[(0,s.jsx)("textarea",{value:r,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),y())},placeholder:"Değerleri girin veya bir soru sorun...",className:"flex-1 resize-none border border-gray-200 rounded-xl px-3 md:px-4 py-2 md:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 placeholder-slate-500 text-sm md:text-base",rows:2,disabled:n}),(0,s.jsx)("button",{onClick:y,disabled:!r.trim()||n,className:"bg-blue-600 text-white rounded-xl px-4 md:px-6 py-2 md:py-3 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})})]})}var b=a(4616),N=a(9869),f=a(1788),y=a(2525),j=a(1284),v=a(4416),w=a(2502),k=a(4065);w.t1.register(w.PP,w.kc,w.FN,w.No,w.hE,w.m_,w.s$,w.Pz);let C=["β=0\xb0","β=15\xb0","β=30\xb0","β=45\xb0","β=60\xb0","β=75\xb0","β=90\xb0"],P=[{bg:"rgba(59, 130, 246, 0.7)",border:"rgb(59, 130, 246)"},{bg:"rgba(34, 197, 94, 0.7)",border:"rgb(34, 197, 94)"},{bg:"rgba(239, 68, 68, 0.7)",border:"rgb(239, 68, 68)"},{bg:"rgba(245, 158, 11, 0.7)",border:"rgb(245, 158, 11)"},{bg:"rgba(168, 85, 247, 0.7)",border:"rgb(168, 85, 247)"},{bg:"rgba(236, 72, 153, 0.7)",border:"rgb(236, 72, 153)"},{bg:"rgba(99, 102, 241, 0.7)",border:"rgb(99, 102, 241)"}];function S(e){let{data:t}=e,a=(0,l.useRef)(null),r=C.map((e,a)=>{let s=t.filter(t=>t["SCB Numuneleri"]===e),l=s.length>0?s.reduce((e,t)=>e+t["KIC (MPa√m)"],0)/s.length:0;return{angle:e,data:s,average:l,color:P[a]||P[0],count:s.length}}).filter(e=>e.count>0),i={datasets:r.map((e,t)=>({label:"".concat(e.angle," (").concat(e.count," numune)"),data:e.data.map(e=>({x:e["Pmax (N)"],y:e["KIC (MPa√m)"]})),backgroundColor:e.color.bg,borderColor:e.color.border,pointRadius:6,pointHoverRadius:8,pointStyle:t%2==0?"circle":"cross"}))};return(0,l.useEffect)(()=>{let e=a.current;if(!e)return;let t=e.ctx,s=e.chartArea;s&&(t.save(),r.forEach((a,l)=>{if(a.average>0){t.strokeStyle=a.color.border.replace("rgb","rgba").replace(")",", 0.8)"),t.setLineDash([5,5]),t.lineWidth=2,t.beginPath();let r=e.scales.y.getPixelForValue(a.average);t.moveTo(s.left,r),t.lineTo(s.right,r),t.stroke(),t.fillStyle=a.color.border,t.font="12px Arial";let i=r-5-15*l;t.fillText("".concat(a.angle," Ort: ").concat(a.average.toFixed(2)),s.right-120,i)}}),t.restore())},[r]),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)("button",{onClick:()=>{let e=a.current;if(!e)return;let t=e.canvas,s=document.createElement("canvas"),l=s.getContext("2d");s.width=t.width,s.height=t.height,l&&(l.fillStyle="#ffffff",l.fillRect(0,0,s.width,s.height),l.drawImage(t,0,0));let r=s.toDataURL("image/jpeg",.95),i=document.createElement("a");i.download="kic-scatter-chart.jpg",i.href=r,document.body.appendChild(i),i.click(),document.body.removeChild(i)},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Grafiği İndir"})]})}),(0,s.jsx)("div",{className:"h-96 w-full",children:(0,s.jsx)(k.Xl,{ref:a,data:i,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:"SCB Kırılma Tokluğu Test Sonu\xe7ları",font:{size:16,weight:"bold"}},tooltip:{callbacks:{label:function(e){let t=e.dataset.label||"";return"".concat(t,": Pmax=").concat(e.parsed.x,"N, KIC=").concat(e.parsed.y,"MPa√m")}}}},scales:{x:{display:!0,title:{display:!0,text:"Pmax (N)",font:{size:14,weight:"bold"}},grid:{display:!0,color:"rgba(0, 0, 0, 0.1)"}},y:{display:!0,title:{display:!0,text:"KIC (MPa√m)",font:{size:14,weight:"bold"}},grid:{display:!0,color:"rgba(0, 0, 0, 0.1)"}}},interaction:{intersect:!1,mode:"point"}}})})]})}function D(e){let{data:t}=e,a=(0,l.useRef)(null),r=[...t].sort((e,t)=>e["COD (mm)"]-t["COD (mm)"]),i={labels:r.map(e=>e["COD (mm)"].toFixed(3)),datasets:[{label:"Y\xfck-COD Eğrisi",data:r.map(e=>e["Y\xfck (kN)"]),borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.1)",borderWidth:2,pointBackgroundColor:"rgb(34, 197, 94)",pointBorderColor:"rgb(34, 197, 94)",pointRadius:4,pointHoverRadius:6,tension:.1,fill:!0}]};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)("button",{onClick:()=>{let e=a.current;if(!e)return;let t=e.canvas,s=document.createElement("canvas"),l=s.getContext("2d");s.width=t.width,s.height=t.height,l&&(l.fillStyle="#ffffff",l.fillRect(0,0,s.width,s.height),l.drawImage(t,0,0));let r=s.toDataURL("image/jpeg",.95),i=document.createElement("a");i.download="load-cod-curve.jpg",i.href=r,document.body.appendChild(i),i.click(),document.body.removeChild(i)},className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Grafiği İndir"})]})}),(0,s.jsx)("div",{className:"h-96 w-full",children:(0,s.jsx)(k.N1,{ref:a,data:i,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!0,text:"Tipik Y\xfck-COD Eğrisi",font:{size:16,weight:"bold"}},tooltip:{callbacks:{label:function(e){let t=r[e.dataIndex]["COD (mm)"];return"Y\xfck: ".concat(e.parsed.y," kN, COD: ").concat(t," mm")}}}},scales:{x:{display:!0,title:{display:!0,text:"\xc7atlak Ağız A\xe7ıklığı Yer Değiştirme (COD - mm)",font:{size:14,weight:"bold"}},grid:{display:!0,color:"rgba(0, 0, 0, 0.1)"}},y:{display:!0,title:{display:!0,text:"Y\xfck (kN)",font:{size:14,weight:"bold"}},grid:{display:!0,color:"rgba(0, 0, 0, 0.1)"},beginAtZero:!0}},interaction:{intersect:!1,mode:"index"}}})})]})}w.t1.register(w.PP,w.kc,w.FN,w.No,w.hE,w.m_,w.s$);let O=["β=0\xb0","β=15\xb0","β=30\xb0","β=45\xb0","β=60\xb0","β=75\xb0","β=90\xb0"];function A(e){let{data:t,onUpdate:a,onRemove:l}=e,r=(()=>{let e={};return O.forEach(a=>{let s=t.filter(e=>e["SCB Numuneleri"]===a);if(s.length>0){let t=s.reduce((e,t)=>e+t["Pmax (N)"],0)/s.length,l=s.reduce((e,t)=>e+t["KIC (MPa√m)"],0)/s.length;e[a]={avgPmax:t,avgKIC:l,count:s.length}}}),e})(),i=e=>({"β=0\xb0":"bg-blue-50 text-blue-800","β=15\xb0":"bg-green-50 text-green-800","β=30\xb0":"bg-red-50 text-red-800","β=45\xb0":"bg-yellow-50 text-yellow-800","β=60\xb0":"bg-purple-50 text-purple-800","β=75\xb0":"bg-pink-50 text-pink-800","β=90\xb0":"bg-indigo-50 text-indigo-800"})[e]||"bg-gray-50 text-gray-800";return(0,s.jsxs)("div",{className:"overflow-x-auto",children:[(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"SCB Numuneleri"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"Pmax (N)"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"KIC (MPa√m)"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[t.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("select",{value:e["SCB Numuneleri"],onChange:e=>a(t,"SCB Numuneleri",e.target.value),className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium",children:O.map(e=>(0,s.jsx)("option",{value:e,children:e},e))})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"number",value:e["Pmax (N)"],onChange:e=>a(t,"Pmax (N)",parseFloat(e.target.value)||0),className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"number",step:"0.01",value:e["KIC (MPa√m)"],onChange:e=>a(t,"KIC (MPa√m)",parseFloat(e.target.value)||0),className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("button",{onClick:()=>l(t),className:"text-red-600 hover:text-red-900",title:"Satırı Sil",children:(0,s.jsx)(y.A,{className:"h-4 w-4"})})})]},t)),Object.keys(r).length>0&&(0,s.jsxs)("tr",{className:"bg-gray-100 border-t-2 border-gray-300",children:[(0,s.jsx)("td",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"ORTALAMA DEĞERLERİ"}),(0,s.jsx)("td",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"Pmax (N)"}),(0,s.jsx)("td",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"KIC (MPa√m)"}),(0,s.jsx)("td",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"İşlemler"})]}),Object.entries(r).map(e=>{let[t,a]=e;return(0,s.jsxs)("tr",{className:"font-semibold ".concat(i(t)),children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[t," ORTALAMA (",a.count," numune)"]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.avgPmax.toFixed(1)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.avgKIC.toFixed(2)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"-"})]},"avg-".concat(t))})]})]}),0===t.length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-700",children:(0,s.jsx)("p",{className:"text-lg font-medium",children:'Hen\xfcz veri bulunmuyor. "Satır Ekle" butonunu kullanarak veri ekleyebilirsiniz.'})})]})}let K=[{"SCB Numuneleri":"β=0\xb0","Pmax (N)":370,"KIC (MPa√m)":.16},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":190,"KIC (MPa√m)":.1},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":195,"KIC (MPa√m)":.1},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":250,"KIC (MPa√m)":.11},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":510,"KIC (MPa√m)":.22},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":320,"KIC (MPa√m)":.14},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":296,"KIC (MPa√m)":.13},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":452,"KIC (MPa√m)":.19},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":384,"KIC (MPa√m)":.16},{"SCB Numuneleri":"β=0\xb0","Pmax (N)":348,"KIC (MPa√m)":.14},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":340,"KIC (MPa√m)":.15},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":720,"KIC (MPa√m)":.31},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":630,"KIC (MPa√m)":.27},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":486,"KIC (MPa√m)":.21},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":398,"KIC (MPa√m)":.17},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":615,"KIC (MPa√m)":.26},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":532,"KIC (MPa√m)":.23},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":478,"KIC (MPa√m)":.21},{"SCB Numuneleri":"β=30\xb0","Pmax (N)":548,"KIC (MPa√m)":.23}],Y=[{"Y\xfck (kN)":0,"COD (mm)":0},{"Y\xfck (kN)":.05,"COD (mm)":.06},{"Y\xfck (kN)":.1,"COD (mm)":.12},{"Y\xfck (kN)":.15,"COD (mm)":.16},{"Y\xfck (kN)":.2,"COD (mm)":.18},{"Y\xfck (kN)":.25,"COD (mm)":.195},{"Y\xfck (kN)":.3,"COD (mm)":.2},{"Y\xfck (kN)":.35,"COD (mm)":.195},{"Y\xfck (kN)":.4,"COD (mm)":.18},{"Y\xfck (kN)":.45,"COD (mm)":.16},{"Y\xfck (kN)":.5,"COD (mm)":.13},{"Y\xfck (kN)":.55,"COD (mm)":.1},{"Y\xfck (kN)":.6,"COD (mm)":.07},{"Y\xfck (kN)":.65,"COD (mm)":.05},{"Y\xfck (kN)":.7,"COD (mm)":.03},{"Y\xfck (kN)":.75,"COD (mm)":.01},{"Y\xfck (kN)":.8,"COD (mm)":0}];function B(){let[e,t]=(0,l.useState)(K),[a,r]=(0,l.useState)(Y),[i,d]=(0,l.useState)(!1),[o,c]=(0,l.useState)(!1),[m,x]=(0,l.useState)(!1),[h,u]=(0,l.useState)(!1),p=e=>{r(a.filter((t,a)=>a!==e))},g=(e,t,s)=>{let l=[...a];l[e]={...l[e],[t]:s},r(l)},w=(e,t)=>{let a=new Blob([[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(a),l=document.createElement("a");l.href=s,l.download=t,l.click(),window.URL.revokeObjectURL(s)};return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-slate-800 flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:(0,s.jsx)(n.A,{className:"h-6 w-6 text-white"})}),"SCB Kırılma Tokluğu Test Sonu\xe7ları"]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>{t([...e,{"SCB Numuneleri":"β=0\xb0","Pmax (N)":0,"KIC (MPa√m)":0}])},className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Satır Ekle"})]}),(0,s.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"CSV Y\xfckle"})]}),(0,s.jsxs)("button",{onClick:()=>w(e,"scb-test-results.csv"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"CSV İndir"})]})]})]}),(0,s.jsx)(A,{data:e,onUpdate:(a,s,l)=>{let r=[...e];r[a]={...r[a],[s]:l},t(r)},onRemove:a=>{t(e.filter((e,t)=>t!==a))}}),(0,s.jsx)("div",{className:"mt-4 flex space-x-2",children:(0,s.jsx)("button",{onClick:()=>d(!i),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium",children:i?"Grafiği Gizle":"KIC Grafiğini Oluştur"})}),i&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"KIC Değerleri Grafiği (Pmax vs KIC)"}),(0,s.jsx)(S,{data:e})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-slate-800 flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-600 rounded-xl mr-4 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105",children:(0,s.jsx)(n.A,{className:"h-6 w-6 text-white"})}),"Y\xfck-COD Eğrisi G\xf6rselleştirme"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{onClick:()=>{r([...a,{"Y\xfck (kN)":0,"COD (mm)":0}])},className:"flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Satır Ekle"})]}),(0,s.jsxs)("button",{onClick:()=>u(!0),className:"flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"CSV Y\xfckle"})]}),(0,s.jsxs)("button",{onClick:()=>w(a,"load-cod-data.csv"),className:"flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"CSV İndir"})]})]})]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"Y\xfck (kN)"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"COD (mm)"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-sm font-bold text-gray-900 uppercase tracking-wider",children:"İşlemler"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((e,t)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"number",step:"0.01",value:e["Y\xfck (kN)"],onChange:e=>g(t,"Y\xfck (kN)",parseFloat(e.target.value)||0),className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"number",step:"0.001",value:e["COD (mm)"],onChange:e=>g(t,"COD (mm)",parseFloat(e.target.value)||0),className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("button",{onClick:()=>p(t),className:"text-red-600 hover:text-red-900",children:(0,s.jsx)(y.A,{className:"h-4 w-4"})})})]},t))})]})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)("button",{onClick:()=>c(!o),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium",children:o?"Grafiği Gizle":"Y\xfck-COD Eğrisini Oluştur"})}),o&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Tipik Y\xfck-COD Eğrisi"}),(0,s.jsx)(D,{data:a})]})]}),m&&(0,s.jsxs)("div",{className:"fixed inset-0 flex items-center justify-center z-50",onClick:()=>x(!1),children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 bg-opacity-60",style:{backdropFilter:"blur(4px)",WebkitBackdropFilter:"blur(4px)"}}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-gray-900 flex items-center",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"SCB CSV Dosyası Y\xfckleme"]}),(0,s.jsx)("button",{onClick:()=>x(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(v.A,{className:"h-5 w-5"})})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("p",{className:"text-base font-semibold text-gray-900 mb-3",children:"CSV dosyanız aşağıdaki formatta olmalıdır:"}),(0,s.jsxs)("div",{className:"bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono",children:[(0,s.jsx)("div",{className:"font-bold text-gray-900 mb-1",children:"SCB Numuneleri,Pmax (N),KIC (MPa√m)"}),(0,s.jsx)("div",{className:"text-gray-800",children:"β=0\xb0,370,0.16"}),(0,s.jsx)("div",{className:"text-gray-800",children:"β=30\xb0,720,0.31"}),(0,s.jsx)("div",{className:"text-gray-800",children:"β=45\xb0,450,0.20"}),(0,s.jsx)("div",{className:"text-gray-600",children:"..."})]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-800 mt-3 space-y-1",children:[(0,s.jsx)("div",{children:"• İlk satır başlık satırı olmalıdır"}),(0,s.jsx)("div",{children:"• Beta a\xe7ıları: β=0\xb0, β=15\xb0, β=30\xb0, β=45\xb0, β=60\xb0, β=75\xb0, β=90\xb0"}),(0,s.jsx)("div",{children:"• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("label",{className:"flex-1",children:[(0,s.jsx)("input",{type:"file",accept:".csv",onChange:e=>{var a;let s=null==(a=e.target.files)?void 0:a[0];if(!s)return;let l=new FileReader;l.onload=e=>{try{var a;let s=(null==(a=e.target)?void 0:a.result).split("\n").filter(e=>e.trim()),l=s[0].split(",").map(e=>e.trim());if(!["SCB Numuneleri","Pmax (N)","KIC (MPa√m)"].every(e=>l.includes(e)))return void alert("CSV formatı hatalı! Başlıklar: SCB Numuneleri, Pmax (N), KIC (MPa√m) olmalıdır.");let r=[];for(let e=1;e<s.length;e++){let t=s[e].split(",").map(e=>e.trim());t.length>=3&&r.push({"SCB Numuneleri":t[0],"Pmax (N)":parseFloat(t[1])||0,"KIC (MPa√m)":parseFloat(t[2])||0})}t(r),x(!1),alert("".concat(r.length," satır başarıyla y\xfcklendi!"))}catch(e){alert("CSV dosyası okunurken hata oluştu!")}},l.readAsText(s),e.target.value=""},className:"hidden"}),(0,s.jsx)("div",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center",children:"Dosya Se\xe7 ve Y\xfckle"})]}),(0,s.jsx)("button",{onClick:()=>x(!1),className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"İptal"})]})]})]}),h&&(0,s.jsxs)("div",{className:"fixed inset-0 flex items-center justify-center z-50",onClick:()=>u(!1),children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 bg-opacity-60",style:{backdropFilter:"blur(4px)",WebkitBackdropFilter:"blur(4px)"}}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl relative z-10",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-gray-900 flex items-center",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"COD CSV Dosyası Y\xfckleme"]}),(0,s.jsx)("button",{onClick:()=>u(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(v.A,{className:"h-5 w-5"})})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("p",{className:"text-base font-semibold text-gray-900 mb-3",children:"CSV dosyanız aşağıdaki formatta olmalıdır:"}),(0,s.jsxs)("div",{className:"bg-gray-50 border-2 border-gray-200 p-4 rounded-md text-sm font-mono",children:[(0,s.jsx)("div",{className:"font-bold text-gray-900 mb-1",children:"Y\xfck (kN),COD (mm)"}),(0,s.jsx)("div",{className:"text-gray-800",children:"0.00,0.00"}),(0,s.jsx)("div",{className:"text-gray-800",children:"0.05,0.06"}),(0,s.jsx)("div",{className:"text-gray-800",children:"0.10,0.12"}),(0,s.jsx)("div",{className:"text-gray-600",children:"..."})]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-800 mt-3 space-y-1",children:[(0,s.jsx)("div",{children:"• İlk satır başlık satırı olmalıdır"}),(0,s.jsx)("div",{children:"• Sayısal değerler (,) ile değil, nokta (.) ile ayrılmalıdır"}),(0,s.jsx)("div",{children:"• Y\xfck değerleri kN cinsinden, COD değerleri mm cinsinden"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("label",{className:"flex-1",children:[(0,s.jsx)("input",{type:"file",accept:".csv",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;let s=new FileReader;s.onload=e=>{try{var t;let a=(null==(t=e.target)?void 0:t.result).split("\n").filter(e=>e.trim()),s=a[0].split(",").map(e=>e.trim());if(!["Y\xfck (kN)","COD (mm)"].every(e=>s.includes(e)))return void alert("CSV formatı hatalı! Başlıklar: Y\xfck (kN), COD (mm) olmalıdır.");let l=[];for(let e=1;e<a.length;e++){let t=a[e].split(",").map(e=>e.trim());t.length>=2&&l.push({"Y\xfck (kN)":parseFloat(t[0])||0,"COD (mm)":parseFloat(t[1])||0})}r(l),u(!1),alert("".concat(l.length," satır başarıyla y\xfcklendi!"))}catch(e){alert("CSV dosyası okunurken hata oluştu!")}},s.readAsText(a),e.target.value=""},className:"hidden"}),(0,s.jsx)("div",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer text-center",children:"Dosya Se\xe7 ve Y\xfckle"})]}),(0,s.jsx)("button",{onClick:()=>u(!1),className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"İptal"})]})]})]})]})}function I(){let[e,t]=(0,l.useState)("chat"),[a,c]=(0,l.useState)([]),m=(0,r.useRouter)(),x=async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),m.push("/login"),m.refresh()}catch(e){console.error("Logout error:",e)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-100",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 md:h-20",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4 min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"p-1.5 md:p-2 bg-white rounded-lg md:rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex-shrink-0",children:(0,s.jsx)(o.default,{src:"/demokrasilogo.png",alt:"Demokrasi \xdcniversitesi Logo",width:32,height:32,className:"object-contain md:w-12 md:h-12"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-lg md:text-2xl font-bold text-slate-800 truncate",children:"Kırılma Tokluğu Asistanı"}),(0,s.jsx)("p",{className:"text-xs md:text-sm text-slate-600 truncate",children:"Yapay Zeka Destekli Hesaplama ve Analiz"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 md:space-x-3 flex-shrink-0",children:[(0,s.jsxs)("button",{onClick:()=>t("chat"),className:"flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ".concat("chat"===e?"bg-blue-600 text-white shadow-md hover:scale-105":"text-slate-700 hover:text-slate-900 hover:bg-gray-50"),children:[(0,s.jsx)(i.A,{className:"h-3 w-3 md:h-4 md:w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Kırılma Tokluğu Asistanı"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Chat"})]}),(0,s.jsxs)("button",{onClick:()=>t("analysis"),className:"flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium transition-all duration-200 ".concat("analysis"===e?"bg-blue-600 text-white shadow-md hover:scale-105":"text-slate-700 hover:text-slate-900 hover:bg-gray-50"),children:[(0,s.jsx)(n.A,{className:"h-3 w-3 md:h-4 md:w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Laboratuvar Veri Analizi"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Analiz"})]}),(0,s.jsxs)("button",{onClick:x,className:"flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-1.5 md:py-2 rounded-lg md:rounded-xl text-xs md:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200 hover:scale-105",title:"\xc7ıkış Yap",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 md:h-4 md:w-4"}),(0,s.jsx)("span",{className:"hidden lg:inline",children:"\xc7ıkış"})]})]})]})})}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["chat"===e&&(0,s.jsx)(g,{messages:a,setMessages:c}),"analysis"===e&&(0,s.jsx)(B,{})]})]})}},5895:(e,t,a)=>{Promise.resolve().then(a.bind(a,2921))}},e=>{var t=t=>e(e.s=t);e.O(0,[647,750,129,441,684,358],()=>t(5895)),_N_E=e.O()}]);